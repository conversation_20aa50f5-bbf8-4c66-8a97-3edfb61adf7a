%% bare_adv.tex
%the solution ofution ofution ofution ofb
%% 2015/08/26
%% by Michael <PERSON>
%% See: 
%% http://www.michaelshell.org/
%% for current contact information.
%%estimating the relative rotation.emonstrating the advanced use of IEEEtran.cls
%% (requires IEEEtran.cls version 1.8b or later) with an IEEE Computer
%% Society journal paper.
%%
%% Support sites:
%% http://www.michaelshell.org/tex/ieeetran/
%% http://www.ctan.org/pkg/ieeetran
%% and
%% http://www.ieee.org/

%%*************************************************************************
%% Legal Notice:
%% This code is offered as-is without any warranty either expressed or
%% implied; without even the implied warranty of MERCHANTABILITY or
%% FITNESS FOR A PARTICULAR PURPOSE! 
%% User assumes all risk.
%% In no event shall the IEEE or any contributor to this code be liable for
%% any damages or losses, including, but not limited to, incidental,
%% consequential, or any other damages, resultingsee the theom the use or misuse
%% of any information contained here.
%%
%% All comments are the opinions of their respective authors and are not
%% necessarily endorsed by the IEEE.
%%
%% This work is distributed under the LaTeX Project Public License (LPPL)
%% ( http://www.latex-project.org/ ) version 1.3, and may be freely used,
%% distributed and modified. A copy of the LPPL, version 1.3, is included
%% in the base LaTeX documentation of all distributions of LaTeX released
%% 2003/12/01 or later.
%% Retain all contribution notices and credits.
%% ** Modified files should be clearly indicated as such, including  **
%% ** renaming them and changing author support contact information. **
%%*************************************************************************


% *** Authors should verify (and, if needed, correct) their LaTeX system  ***
% *** with the testflow diagnostic prior to trusting their LaTeX platform ***
% *** with production work. The IEEE's font choices and paper sizes can   ***
% *** trigger bugs that do not appear when using other class files.       ***                          ***
% The testflow support page is at:
% http://www.michaelshell.org/tex/testflow/


% IEEEtran V1.7 and later provides for these CLASSINPUT macros to allow the
% user to reprogram some IEEEtran.cls defaults if needed. These settings
% override the internal defaults of IEEEtran.cls regardless of which class
% options are used. Do not use these unless you have good reason to do so as
% they can result in nonIEEE compliant documents. User beware. ;)
%
%\newcommand{\CLASSINPUTbaselinestretch}{1.0} % baselinestretch
%\newcommand{\CLASSINPUTinnersidemargin}{1in} % inner side margin
%\newcommand{\CLASSINPUToutersidemargin}{1in} % outer side margin
%\newcommand{\CLASSINPUTtoptextmargin}{1in}   % top text margin
%\newcommand{\CLASSINPUTbottomtextmargin}{1in}% bottom text margin




%
\documentclass[10pt,journal,compsoc]{IEEEtran}
% If IEEEtran.cls has not been installed into the LaTeX system files,
% manually specify the path to it like:
% \documentclass[10pt,journal,compsoc]{../sty/IEEEtran}


% For Computer Society journals, IEEEtran defaults to the use of 
% Palatino/Palladio as is done in IEEE Computer Society journals.
% To go back to Times Roman, you can use this code:
%\renewcommand{\rmdefault}{ptm}\selectfont





% Some very useful LaTeX packages include:
% (uncomment the ones you want to load)



% *** MISC UTILITY PACKAGES ***
%
%\usepackage{ifpdf}
% Heiko Oberdiek's ifpdf.sty is very useful if you need conditional
% compilation based on whether the output is pdf or dvi.
% usage:
% \ifpdf
%   % pdf code
% \else
%   % dvi code
% \fi
% The latest version of ifpdf.sty can be obtained from:
% http://www.ctan.org/pkg/ifpdf
% Also, note that IEEEtran.cls V1.7 and later provides a builtin
% \ifCLASSINFOpdf conditional that works the same way.
% When switching from latex to pdflatex and vice-versa, the compiler may
% have to be run twice to clear warning/error messages.

\usepackage{amsmath,amsfonts}
% \usepackage{algorithmic}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{adjustbox}
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{soul,color,xcolor}
% \usepackage{subfig}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{array}
\usepackage{multirow}
\usepackage{subcaption}
\usepackage{amsthm}
\usepackage[font=small,labelfont=bf]{caption}
\usepackage{subfig}
\usepackage{geometry}
\usepackage{enumitem}  % 用于定制列表
\usepackage{threeparttable}

\geometry{a4paper, margin=1in}

\captionsetup[subfloat]{font=scriptsize, labelfont=scriptsize}
% 定义proposition环境并自定义样式 
\renewcommand{\arraystretch}{1.5}


\newtheoremstyle{leftalign} 
{3pt} % Space above 
{3pt} % Space below 
{} % Body font 
{} % Indent amount 
{\bfseries} % Theorem head font 
{.} % Punctuation after theorem head 
{ } % Space after theorem head 
{} % Theorem head spec 

\theoremstyle{leftalign} 
\newtheorem{proposition}{Proposition}

% *** CITATION PACKAGES ***
%
\ifCLASSOPTIONcompsoc
  % IEEE Computer Society needs nocompress option
  % requires cite.sty v4.0 or later (November 2003)
  \usepackage[nocompress]{cite}
\else
  % normal IEEE
  \usepackage{cite}
\fi





% *** CITATION PACKAGES ***
%
\ifCLASSOPTIONcompsoc
  % The IEEE Computer Society needs nocompress option
  % requires cite.sty v4.0 or later (November 2003)
  \usepackage[nocompress]{cite}
\else
  % normal IEEE
  \usepackage{cite}
\fi
% cite.sty was written by Donald Arseneau
% V1.6 and later of IEEEtran pre-defines the format of the cite.sty package
% \cite{} output to follow that of the IEEE. Loading the cite package will
% result in citation numbers being automatically sorted and properly
% "compressed/ranged". e.g., [1], [9], [2], [7], [5], [6] without using
% cite.sty will become [1], [2], [5]--[7], [9] using cite.sty. cite.sty's
% \cite will automatically add leading space, if needed. Use cite.sty's
% noadjust option (cite.sty V3.8 and later) if you want to turn this off
% such as if a citation ever needs to be enclosed in parenthesis.
% cite.sty is already installed on most LaTeX systems. Be sure and use
% version 5.0 (2009-03-20) and later if using hyperref.sty.
% The latest version can be obtained at:
% http://www.ctan.org/pkg/cite
% The documentation is contained in the cite.sty file itself.
%
% Note that some packages require special options to format as the Computer
% Society requires. In particular, Computer Society  papers do not use
% compressed citation ranges as is done in typical IEEE papers
% (e.g., [1]-[4]). Instead, they list every citation separately in order
% (e.g., [1], [2], [3], [4]). To get the latter we need to load the cite
% package with the nocompress option which is supported by cite.sty v4.0
% and later.





% *** GRAPHICS RELATED PACKAGES ***
%
\ifCLASSINFOpdf
  % \usepackage[pdftex]{graphicx}
  % declare the path(s) where your graphic files are
  % \graphicspath{{../pdf/}{../jpeg/}}
  % and their extensions so you won't have to specify these with
  % every instance of \includegraphics
  % \DeclareGraphicsExtensions{.pdf,.jpeg,.png}
\else
  % or other class option (dvipsone, dvipdf, if not using dvips). graphicx
  % will default to the driver specified in the system graphics.cfg if no
  % driver is specified.
  % \usepackage[dvips]{graphicx}
  % declare the path(s) where your graphic files are
  % \graphicspath{{../eps/}}
  % and their extensions so you won't have to specify these with
  % every instance of \includegraphics
  % \DeclareGraphicsExtensions{.eps}
\fi
% graphicx was written by David Carlisle and Sebastian Rahtz. It is
% required if you want graphics, photos, etc. graphicx.sty is already
% installed on most LaTeX systems. The latest version and documentation
% can be obtained at: 
% http://www.ctan.org/pkg/graphicx
% Another good source of documentation is "Using Imported Graphics in
% LaTeX2e" by Keith Reckdahl which can be found at:
% http://www.ctan.org/pkg/epslatex
%
% latex, and pdflatex in dvi mode, support graphics in encapsulated
% postscript (.eps) format. pdflatex in pdf mode supports graphics
% in .pdf, .jpeg, .png and .mps (metapost) formats. Users should ensure
% that all non-photo figures use a vector format (.eps, .pdf, .mps) and
% not a bitmapped formats (.jpeg, .png). The IEEE frowns on bitmapped formats
% which can result in "jaggedy"/blurry rendering of lines and letters as
% well as large increases in file sizes.
%
% You can find documentation about the pdfTeX application at:
% http://www.tug.org/applications/pdftex





% *** MATH PACKAGES ***
%
%\usepackage{amsmath}
% A popular package from the American Mathematical Society that provides
% many useful and powerful commands for dealing with mathematics.
%
% Note that the amsmath package sets \interdisplaylinepenalty to 10000
% thus preventing page breaks from occurring within multiline equations. Use:
%\interdisplaylinepenalty=2500
% after loading amsmath to restore such page breaks as IEEEtran.cls normally
% does. amsmath.sty is already installed on most LaTeX systems. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/amsmath





% *** SPECIALIZED LIST PACKAGES ***
%\usepackage{acronym}
% acronym.sty was written by Tobias Oetiker. This package provides tools for
% managing documents with large numbers of acronyms. (You don't *have* to
% use this package - unless you have a lot of acronyms, you may feel that
% such package management of them is bit of an overkill.)
% Do note that the acronym environment (which lists acronyms) will have a
% problem when used under IEEEtran.cls because acronym.sty relies on the
% description list environment - which IEEEtran.cls has customized for
% producing IEEE style lists. A workaround is to declared the longest
% label width via the IEEEtran.cls \IEEEiedlistdecl global control:
%
% \renewcommand{\IEEEiedlistdecl}{\IEEEsetlabelwidth{SONET}}
% \begin{acronym}
%
% \end{acronym}
% \renewcommand{\IEEEiedlistdecl}{\relax}% remember to reset \IEEEiedlistdecl
%
% instead of using the acronym environment's optional argument.
% The latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/acronym


%\usepackage{algorithmic}
% algorithmic.sty was written by Peter Williams and Rogerio Brito.
% This package provides an algorithmic environment fo describing algorithms.
% You can use the algorithmic environment in-text or within a figure
% environment to provide for a floating algorithm. Do NOT use the algorithm
% floating environment provided by algorithm.sty (by the same authors) or
% algorithm2e.sty (by Christophe Fiorio) as the IEEE does not use dedicated
% algorithm float types and packages that provide these will not provide
% correct IEEE style captions. The latest version and documentation of
% algorithmic.sty can be obtained at:
% http://www.ctan.org/pkg/algorithms
% Also of interest may be the (relatively newer and more customizable)
% algorithmicx.sty package by Szasz Janos:
% http://www.ctan.org/pkg/algorithmicx




% *** ALIGNMENT PACKAGES ***
%
%\usepackage{array}
% Frank Mittelbach's and David Carlisle's array.sty patches and improves
% the standard LaTeX2e array and tabular environments to provide better
% appearance and additional user controls. As the default LaTeX2e table
% generation code is lacking to the point of almost being broken with
% respect to the quality of the end results, all users are strongly
% advised to use an enhanced (at the very least that provided by array.sty)
% set of table tools. array.sty is already installed on most systems. The
% latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/array


%\usepackage{mdwmath}
%\usepackage{mdwtab}
% Also highly recommended is Mark Wooding's extremely powerful MDW tools,
% especially mdwmath.sty and mdwtab.sty which are used to format equations
% and tables, respectively. The MDWtools set is already installed on most
% LaTeX systems. The lastest version and documentation is available at:
% http://www.ctan.org/pkg/mdwtools


% IEEEtran contains the IEEEeqnarray family of commands that can be used to
% generate multiline equations as well as matrices, tables, etc., of high
% quality.


%\usepackage{eqparbox}
% Also of notable interest is Scott Pakin's eqparbox package for creating
% (automatically sized) equal width boxes - aka "natural width parboxes".
% Available at:
% http://www.ctan.org/pkg/eqparbox




% *** SUBFIGURE PACKAGES ***
%\ifCLASSOPTIONcompsoc
%  \usepackage[caption=false,font=footnotesize,labelfont=sf,textfont=sf]{subfig}
%\else
%  \usepackage[caption=false,font=footnotesize]{subfig}
%\fi
% subfig.sty, written by Steven Douglas Cochran, is the modern replacement
% for subfigure.sty, the latter of which is no longer maintained and is
% incompatible with some LaTeX packages including fixltx2e. However,
% subfig.sty requires and automatically loads Axel Sommerfeldt's caption.sty
% which will override IEEEtran.cls' handling of captions and this will result
% in non-IEEE style figure/table captions. To prevent this problem, be sure
% and invoke subfig.sty's "caption=false" package option (available since
% subfig.sty version 1.3, 2005/06/28) as this is will preserve IEEEtran.cls
% handling of captions.
% Note that the Computer Society format requires a sans serif font rather
% than the serif font used in traditional IEEE formatting and thus the need
% to invoke different subfig.sty package options depending on whether
% compsoc mode has been enabled.
%
% The latest version and documentation of subfig.sty can be obtained at:
% http://www.ctan.org/pkg/subfig




% *** FLOAT PACKAGES ***
%
%\usepackage{fixltx2e}
% fixltx2e, the successor to the earlier fix2col.sty, was written by
% Frank Mittelbach and David Carlisle. This package corrects a few problems
% in the LaTeX2e kernel, the most notable of which is that in current
% LaTeX2e releases, the ordering of single and double column floats is not
% guaranteed to be preserved. Thus, an unpatched LaTeX2e can allow a
% single column figure to be placed prior to an earlier double column
% figure.
% Be aware that LaTeX2e kernels dated 2015 and later have fixltx2e.sty's
% corrections already built into the system in which case a warning will
% be issued if an attempt is made to load fixltx2e.sty as it is no longer
% needed.
% The latest version and documentation can be found at:
% http://www.ctan.org/pkg/fixltx2e


%\usepackage{stfloats}
% stfloats.sty was written by Sigitas Tolusis. This package gives LaTeX2e
% the ability to do double column floats at the bottom of the page as well
% as the top. (e.g., "\begin{figure*}[!b]" is not normally possible in
% LaTeX2e). It also provides a command:
%\fnbelowfloat
% to enable the placement of footnotes below bottom floats (the standard
% LaTeX2e kernel puts them above bottom floats). This is an invasive package
% which rewrites many portions of the LaTeX2e float routines. It may not work
% with other packages that modify the LaTeX2e float routines. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/stfloats
% Do not use the stfloats baselinefloat ability as the IEEE does not allow
% \baselineskip to stretch. Authors submitting work to the IEEE should note
% that the IEEE rarely uses double column equations and that authors should try
% to avoid such use. Do not be tempted to use the cuted.sty or midfloat.sty
% packages (also by Sigitas Tolusis) as the IEEE does not format its papers in
% such ways.
% Do not attempt to use stfloats with fixltx2e as they are incompatible.
% Instead, use Morten Hogholm'a dblfloatfix which combines the features
% of both fixltx2e and stfloats:
%
% \usepackage{dblfloatfix}
% The latest version can be found at:
% http://www.ctan.org/pkg/dblfloatfix


%\ifCLASSOPTIONcaptionsoff
%  \usepackage[nomarkers]{endfloat}
% \let\MYoriglatexcaption\caption
% \renewcommand{\caption}[2][\relax]{\MYoriglatexcaption[#2]{#2}}
%\fi
% endfloat.sty was written by James Darrell McCauley, Jeff Goldberg and 
% Axel Sommerfeldt. This package may be useful when used in conjunction with 
% IEEEtran.cls'  captionsoff option. Some IEEE journals/societies require that
% submissions have lists of figures/tables at the end of the paper and that
% figures/tables without any captions are placed on a page by themselves at
% the end of the document. If needed, the draftcls IEEEtran class option or
% \CLASSINPUTbaselinestretch interface can be used to increase the line
% spacing as well. Be sure and use the nomarkers option of endfloat to
% prevent endfloat from "marking" where the figures would have been placed
% in the text. The two hack lines of code above are a slight modification of
% that suggested by in the endfloat docs (section 8.4.1) to ensure that
% the full captions always appear in the list of figures/tables - even if
% the user used the short optional argument of \caption[]{}.
% IEEE papers do not typically make use of \caption[]'s optional argument,
% so this should not be an issue. A similar trick can be used to disable
% captions of packages such as subfig.sty that lack options to turn off
% the subcaptions:
% For subfig.sty:
% \let\MYorigsubfloat\subfloat
% \renewcommand{\subfloat}[2][\relax]{\MYorigsubfloat[]{#2}}
% However, the above trick will not work if both optional arguments of
% the \subfloat command are used. Furthermore, there needs to be a
% description of each subfigure *somewhere* and endfloat does not add
% subfigure captions to its list of figures. Thus, the best approach is to
% avoid the use of subfigure captions (many IEEE journals avoid them anyway)
% and instead reference/explain all the subfigures within the main caption.
% The latest version of endfloat.sty and its documentation can obtained at:
% http://www.ctan.org/pkg/endfloat
%
% The IEEEtran \ifCLASSOPTIONcaptionsoff conditional can also be used
% later in the document, say, to conditionally put the References on a 
% page by themselves.





% *** PDF, URL AND HYPERLINK PACKAGES ***
%
%\usepackage{url}
% url.sty was written by Donald Arseneau. It provides better support for
% handling and breaking URLs. url.sty is already installed on most LaTeX
% systems. The latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/url
% Basically, \url{my_url_here}.


% NOTE: PDF thumbnail features are not required in IEEE papers
%       and their use requires extra complexity and work.
%\ifCLASSINFOpdf
%  \usepackage[pdftex]{thumbpdf}
%\else
%  \usepackage[dvips]{thumbpdf}
%\fi
% thumbpdf.sty and its companion Perl utility were written by Heiko Oberdiek.
% It allows the user a way to produce PDF documents that contain fancy
% thumbnail images of each of the pages (which tools like acrobat reader can
% utilize). This is possible even when using dvi->ps->pdf workflow if the
% correct thumbpdf driver options are used. thumbpdf.sty incorporates the
% file containing the PDF thumbnail information (filename.tpm is used with
% dvips, filename.tpt is used with pdftex, where filename is the base name of
% your tex document) into the final ps or pdf output document. An external
% utility, the thumbpdf *Perl script* is needed to make these .tpm or .tpt
% thumbnail files from a .ps or .pdf version of the document (which obviously
% does not yet contain pdf thumbnails). Thus, one does a:
% 
% thumbpdf filename.pdf 
%
% to make a filename.tpt, and:
%
% thumbpdf --mode dvips filename.ps
%
% to make a filename.tpm which will then be loaded into the document by
% thumbpdf.sty the NEXT time the document is compiled (by pdflatex or
% latex->dvips->ps2pdf). Users must be careful to regenerate the .tpt and/or
% .tpm files if the main document changes and then to recompile the
% document to incorporate the revised thumbnails to ensure that thumbnails
% match the actual pages. It is easy to forget to do this!
% 
% Unix systems come with a Perl interpreter. However, MS Windows users
% will usually have to install a Perl interpreter so that the thumbpdf
% script can be run. The Ghostscript PS/PDF interpreter is also required.
% See the thumbpdf docs for details. The latest version and documentation
% can be obtained at.
% http://www.ctan.org/pkg/thumbpdf


% NOTE: PDF hyperlink and bookmark features are not required in IEEE
%       papers and their use requires extra complexity and work.
% *** IF USING HYPERREF BE SURE AND CHANGE THE EXAMPLE PDF ***
% *** TITLE/SUBJECT/AUTHOR/KEYWORDS INFO BELOW!!           ***
\newcommand\MYhyperrefoptions{bookmarks=true,bookmarksnumbered=true,
pdfpagemode={UseOutlines},plainpages=false,pdfpagelabels=true,
colorlinks=true,linkcolor={black},citecolor={black},urlcolor={black},
pdftitle={Bare Demo of IEEEtran.cls for Computer Society Journals},%<!CHANGE!
pdfsubject={Typesetting},%<!CHANGE!
pdfauthor={Michael D. Shell},%<!CHANGE!
pdfkeywords={Computer Society, IEEEtran, journal, LaTeX, paper,
             template}}%<^!CHANGE!
%\ifCLASSINFOpdf
%\usepackage[\MYhyperrefoptions,pdftex]{hyperref}
%\else
%\usepackage[\MYhyperrefoptions,breaklinks=true,dvips]{hyperref}
%\usepackage{breakurl}
%\fi
% One significant drawback of using hyperref under DVI output is that the
% LaTeX compiler cannot break URLs across lines or pages as can be done
% under pdfLaTeX's PDF output via the hyperref pdftex driver. This is
% probably the single most important capability distinction between the
% DVI and PDF output. Perhaps surprisingly, all the other PDF features
% (PDF bookmarks, thumbnails, etc.) can be preserved in
% .tex->.dvi->.ps->.pdf workflow if the respective packages/scripts are
% loaded/invoked with the correct driver options (dvips, etc.). 
% As most IEEE papers use URLs sparingly (mainly in the references), this
% may not be as big an issue as with other publications.
%
% That said, Vilar Camara Neto created his breakurl.sty package which
% permits hyperref to easily break URLs even in dvi mode.
% Note that breakurl, unlike most other packages, must be loaded
% AFTER hyperref. The latest version of breakurl and its documentation can
% be obtained at:
% http://www.ctan.org/pkg/breakurl
% breakurl.sty is not for use under pdflatex pdf mode.
%
% The advanced features offer by hyperref.sty are not required for IEEE
% submission, so users should weigh these features against the added
% complexity of use.
% The package options above demonstrate how to enable PDF bookmarks
% (a type of table of contents viewable in Acrobat Reader) as well as
% PDF document information (title, subject, author and keywords) that is
% viewable in Acrobat reader's Document_Properties menu. PDF document
% information is also used extensively to automate the cataloging of PDF
% documents. The above set of options ensures that hyperlinks will not be
% colored in the text and thus will not be visible in the printed page,
% but will be active on "mouse over". USING COLORS OR OTHER HIGHLIGHTING
% OF HYPERLINKS CAN RESULT IN DOCUMENT REJECTION BY THE IEEE, especially if
% these appear on the "printed" page. IF IN DOUBT, ASK THE RELEVANT
% SUBMISSION EDITOR. You may need to add the option hypertexnames=false if
% you used duplicate equation numbers, etc., but this should not be needed
% in normal IEEE work.
% The latest version of hyperref and its documentation can be obtained at:
% http://www.ctan.org/pkg/hyperref





% *** Do not adjust lengths that control margins, column widths, etc. ***
% *** Do not use packages that alter fonts (such as pslatex).         ***
% There should be no need to do such things with IEEEtran.cls V1.6 and later.
% (Unless specifically asked to do so by the journal or conference you plan
% to submit to, of course. )


% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}


\begin{document}
%
% paper title
% Titles are generally capitalized except for words such as a, an, and, as,
% at, but, by, for, in, nor, of, on, or, the, to and up, which are usually
% not capitalized unless they are the first or last word of the title.
% Linebreaks \\ can be used within to get better formatting as desired.
% Do not put math or special symbols in the title.
\title{Linear Relative Pose Estimation \\Founded on Pose-only Imaging Geometry}
%
%
% author names and IEEE memberships
% note positions of commas and nonbreaking spaces ( ~ ) LaTeX will not break
% a structure at a ~ so this keeps an author's name from being broken across
% two lines.
% use \thanks{} to gain access to the first footnote area
% a separate \thanks must be used for each paragraph as LaTeX2e's \thanks
% was not built to handle multiple paragraphs
%
%
%\IEEEcompsocitemizethanks is a special \thanks that produces the bulleted
% lists the Computer Society journals use for "first footnote" author
% affiliations. Use \IEEEcompsocthanksitem which works much like \item
% for each affiliation group. When not in compsoc mode,
% \IEEEcompsocitemizethanks becomes like \thanks and
% \IEEEcompsocthanksitem becomes a line break with idention. This
% facilitates dual compilation, although admittedly the differences in the
% desired content of \author between the different types of papers makes a
% one-size-fits-all approach a daunting prospect. For instance, compsoc 
% journal papers have the author affiliations above the "Manuscript
% received ..."  text while in non-compsoc journals this is reversed. Sigh.

\author{Qi~Cai,~Xinrui~Li,
        Yuanxin~Wu,~\IEEEmembership{Senior~Member,~IEEE,}
        and Wenxian~Yu,~\IEEEmembership{Senior~Member,~IEEE}% <-this % stops a space
\IEEEcompsocitemizethanks{\IEEEcompsocthanksitem Qi~Cai, Xinrui~Li, Yuanxin~Wu, and Wenxian Yu are with the Shanghai Key Laboratory of Navigation and Location-based Services, School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China.
E-mail: <EMAIL>, <EMAIL>, yuanx\<EMAIL>, <EMAIL>.}% <-this % stops a space
\thanks{}}


% note the % following the last \IEEEmembership and also \thanks - 
% these prevent an unwanted space from occurring between the last author name
% and the end of the author line. i.e., if you had this:
% 
% \author{....lastname \thanks{...} \thanks{...} }
%                     ^------------^------------^----Do not want these spaces!
%
% a space would be appended to the last name and could cause every name on that
% line to be shifted left slightly. This is one of those "LaTeX things". For
% instance, "\textbf{A} \textbf{B}" will typeset as "A B" not "AB". To get
% "AB" then you have to do: "\textbf{A}\textbf{B}"
% \thanks is no different in this regard, so shield the last } of each \thanks
% that ends a line with a % and do not let a space in before the next \thanks.
% Spaces after \IEEEmembership other than the last one are OK (and needed) as
% you are supposed to have spaces between the names. For what it is worth,
% this is a minor point as most people would not even notice if the said evil
% space somehow managed to creep in.



% The paper headers
\markboth{IEEE TRANSACTIONS ON PATTERN ANALYSIS AND MACHINE INTELLIGENCE,~Vol.~XX, No.~XX, June~2024}%
{Cai \MakeLowercase{\textit{et al.}}: Linear Relative Pose Estimation Founded on Pose-only Imaging Geometry}
% The only time the second header will appear is for the odd numbered pages
% after the title page when using the twoside option.
% 
% *** Note that you probably will NOT want to include the author's ***
% *** name in the headers of peer review papers.                   ***
% You can use \ifCLASSOPTIONpeerreview for conditional compilation here if
% you desire.



% The publisher's ID mark at the bottom of the page is less important with
% Computer Society journal papers as those publications place the marks
% outside of the main text columns and, therefore, unlike regular IEEE
% journals, the available text space is not reduced by their presence.
% If you want to put a publisher's ID mark on the page you can do it like
% this:
%\IEEEpubid{0000--0000/00\$00.00~\copyright~2015 IEEE}
% or like this to get the Computer Society new two part style.
%\IEEEpubid{\makebox[\columnwidth]{\hfill 0000--0000/00/\$00.00~\copyright~2015 IEEE}%
%\hspace{\columnsep}\makebox[\columnwidth]{Published by the IEEE Computer Society\hfill}}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark (Computer Society journal
% papers don't need this extra clearance.)



% use for special paper notices
%\IEEEspecialpapernotice{(Invited Paper)}



% for Computer Society papers, we must declare the abstract and index terms
% PRIOR to the title within the \IEEEtitleabstractindextext IEEEtran
% command as these need to go into the title area created by \maketitle.
% As a general rule, do not put math, special symbols or citations
% in the abstract or keywords.
\IEEEtitleabstractindextext{%
\begin{abstract}

Two-view relative pose solution is usually entangled with feature-matching outlier handling and 3D reconstruction. This paper presents a general solution form to the essential equation considering planar scenes and pure rotational motions and establishes its relationship to the homography matrix in planar cases.
The essential matrix is linearly obtained for the first time by solving the N-point problem. We introduce the pose-only imaging geometrical constraints into the relative pose estimation pipeline to effectively screen out feature matching outliers and identify the correct relative pose (without 3D reconstruction). We utilize the strategies of graduated non-convex iteratively reweighted least-squares (GNC-IRLS) and RANSAC for further robustness improvement. Simulations and real tests datasets show that the proposed algorithm substantially improves the relative rotation estimation accuracy by 2 $\sim$ 10 times in face of as large as 70\% outliers.
\end{abstract}

% Note that keywords are not normally used for peerreview papers.
\begin{IEEEkeywords}
Pose estimation, 3D visual computation, Outlier Removal, Two-view Geometry 
\end{IEEEkeywords}}


% make the title area
\maketitle


% To allow for easy dual compilation without having to reenter the
% abstract/keywords data, the \IEEEtitleabstractindextext text will
% not be used in maketitle, but will appear (i.e., to be "transported")
% here as \IEEEdisplaynontitleabstractindextext when compsoc mode
% is not selected <OR> if conference mode is selected - because compsoc
% conference papers position the abstract like regular (non-compsoc)
% papers do!
\IEEEdisplaynontitleabstractindextext
% \IEEEdisplaynontitleabstractindextext has no effect when using
% compsoc under a non-conference mode.


% For peer review papers, you can put extra information on the cover
% page as needed:
% \ifCLASSOPTIONpeerreview
% \begin{center} \bfseries EDICS Category: 3-BBND \end{center}
% \fi
%
% For peerreview papers, this IEEEtran command inserts a page break and
% creates the second title. It will be ignored for other modes.
\IEEEpeerreviewmaketitle


\ifCLASSOPTIONcompsoc
\IEEEraisesectionheading{\section{Introduction}\label{sec:introduction}}
\else
\section{Introduction}
\label{sec:intro}
\fi
% Computer Society journal (but not conference!) papers do something unusual
% with the very first section heading (almost always called "Introduction").
% They place it ABOVE the main text! IEEEtran.cls does not automatically do
% this for you, but you can achieve this effect with the provided
% \IEEEraisesectionheading{} command. Note the need to keep any \label that
% is to refer to the section immediately after \section in the above as
% \IEEEraisesectionheading puts \section within a raised box.




% The very first letter is a 2 line initial drop letter followed
% by the rest of the first word in caps (small caps for compsoc).
% 
% form to use if the first word consists of a single letter:
% \IEEEPARstart{A}{demo} file is ....
% 
% form to use if you need the single drop letter followed by
% normal text (unknown if ever used by the IEEE):
% \IEEEPARstart{A}{}demo file is ....
% 
% Some journals put the first two words in caps:
% \IEEEPARstart{T}{his demo} file is ....
% 
% Here we have the typical use of a "T" for an initial drop letter
% and "HIS" in caps to complete the first word.
\IEEEPARstart{T}{he} popular pipeline in three-dimension (3D) visual computing~\cite{ozyecsil2017survey,szeliski2022computer}, such as simultaneous localization and mapping (SLAM) and structure-from-motion (SfM), involves: 1) extracting, matching, and tracking image feature points; 2) removing image feature-matching outliers through visual geometric constraints, such as the fundamental/essential/homography matrix equation; 3) estimating camera poses; and 4) reconstructing the 3D scene. Accurate and robust relative pose estimation, crucial for SLAM and SfM, involves handling  feature-matching outliers and degeneracies in solving  visual geometric constraints (especially the essential equation). 

Solving the essential equation has faced long-standing degeneracy problems such as rank deficiency and multiple solutions ~\cite{decker2008dealing,nister2004efficient,stewenius2006recent,kneip2012finding}. Those degeneracy problems result from an inherent loss of imaging geometry constraints due to the co-planar representation~\cite{cai2019equivalent,kneip2012finding}. They often arise in scenarios involving pure rotational motion, planar structures, specific quadric surfaces, or scenes with all 3D points lying on a line. Specifically, for degeneracies caused by pure rotational motion and planar structures, the correct relative rotation can be recovered from multiple solutions~\cite{cai2019equivalent,enqvist2011non,nister2004efficient,stewenius2006recent} by using a proper identification scheme. However, the popular identification scheme~\cite{longuet1981computer,hartley2003multiple} of reconstructing 3D points and then applying the chirality constraint is unstable in identifying the right solution from multiple candidates. Unsuccessful relative pose identification would inevitably bring great difficulties to outlier handling and vice versa.

Furthermore, regarding the N-point problem~\cite{zhao2020efficient} (using as many matching features as possible), the essential matrix is usually obtained by an initial solver~\cite{stewenius2006recent,pizarro2003relative,hartley2003multiple,longuet1981computer} (including 5/6/7/8pt) or nonlinear optimization~\cite{kneip2014opengv,hartley2003multiple,zhao2020efficient}.  However, the current N-point methods face robustness issues under various degenerate conditions, for example the classic 8pt algorithm and recent convex optimization approach~\cite{zhao2020efficient} cannot deal with the planar case. In fact, under the degenerate planar scenes or pure rotational motion, the essential equation for the N-point problem theoretically imposes six independent constraints on the essential matrix~\cite{Philip1998,pizarro2003relative}. However, the popular 5pt algorithm uses only five independent constraints~\cite{stewenius2006recent}, which actually introduces one extra dimension to the solution space and thus increases the risk of instability in estimating the essential matrix, especially in the presence of a high proportion of outliers. 
%这里补充下?：huang等人指出6点以上的解，是唯一的；Pizarro指出6点法理论上能应对平面退化，但Nister等人认为当前基于6点法的方案，无法稳定地处理平面退化问题。

%Regarding pure rotational degeneracy, \cite{cai2019equivalent,enqvist2011non} found that relative rotation can be correctly solved even in cases of pure rotational degeneracy. Cai et al.~\cite{cai2019equivalent} further provided rigorous proof by analyzing the general solution form of the essential matrix under pure rotation. For planar degeneracy, Nister et al.~\cite{nister2004efficient} proposed the typical five-point algorithm, which uses exactly five point pairs to construct five independent constraints of the essential matrix equation and to find the solution in the four-dimensional solution space~\cite{nister2004efficient,stewenius2006recent} by creating an action matrix based on the Grobner basis~\cite{stewenius2006recent}. 

Feature-matching outliers often occur in challenging scenarios such as insufficient lighting, occlusions, moving objects, and repetitive textures~\cite{Jing2023TPAMI_survey}. These outliers, if not handled properly, would significantly degrade the accuracy of the essential matrix solution ~\cite{longuet1981computer} in two-view relative pose estimation. As a result, the handling of feature-matching outliers has consistently been a primary research focus in the field. Common techniques for handling outliers include RANSAC~\cite{fischler1981random}, which utilizes random sampling and consistency checks combined with geometric constraints such as coplanar or homography equations, to segregate inliers from outliers and estimate the essential, fundamental, or homography matrix. These methods typically require that the randomly sampled subsets consist entirely of inliers and a number of parameters (e.g., inlier thresholds) be delicately tuned. Furthermore, they are computationally expensive, might fail in challenging conditions~\cite{ozyecsil2017survey}, and need to be further improved~\cite{rousseeuw1984least,moulon2013adaptive,barath2019magsac,barath2020magsac++,li2020gesac}. 
Other robust estimation methods also play a crucial role in the rejection of outliers, including M-estimation~\cite{huber1992robust,huber2004robust} and iteratively reweighted least-squares (IRLS)~\cite{huber1992robust}. For instance, the recent works~\cite{li2020gesac} and~\cite{peng2023convergence} integrated IRLS with the RANSAC method and the graduated non-convex (GNC) strategy, respectively, both improving the outlier removal in 3D point registration. 
% M-estimation is more suitable for optimization problems, whereas, in two-view estimation, IRLS methods generally perform significantly worse than RANSAC-like methods, especially in the presence of a high proportion of outliers~\cite{meer1991robust}.  
%Given the outstanding performance of these methods, it suggests that merging GNC-IRLS with RANSAC could further enhance two-view estimation by leveraging the strengths of both approaches.

Our recent pose-only imaging geometry~\cite{cai2021pose} proposed a low-dimensional equivalent representation to the classical multiple-view geometry, by decoupling the 3D scene from the camera poses. Given the global rotations, the global translation was linearly and accurately solved by the proposed linear global translation (LiGT) algorithm~\cite{moulon2022}. It implies that we could only focus on the accuracy of relative rotations in two-view estimation, as well as the removal of matching outliers.

Founded on the pose-only imaging geometry~\cite{cai2019equivalent,cai2021pose}, this paper aims to address the following challenges:

- How to linearly solve the essential equation for the N-point problem, in face of both planar scenes and specific motion degeneracies?

- How to tackle the two tightly-coupled problems of relative pose ambiguity and feature-matching outlier rejection, without incorporating 3D reconstruction?


%n particular, the LiGT algorithm was embedded into the core library of OpenMVG~\cite{moulon2017openmvg} for global translation estimation, which is able to solve global translations on given rotations of all views linearly. It can handle some specific degenerate motions, such as local co-linearity, parallel rigidity, and pure rotational motion~\cite{cai2021pose}.


The primary contributions include:

1. We present a general solution form to the essential equation considering planar scenes and pure rotational motions, and establish its relationship to the homography matrix in planar cases. In particular, the homography matrix can be linearly represented by the three smallest singular vectors of the general solution to the essential equation. 

2. The essential matrix is linearly obtained for the first time by solving the N-point problem. We introduce the pose-only imaging geometrical constraints into the relative pose estimation pipeline to effectively screen out feature matching outliers and identify the correct relative pose (without 3D reconstruction). 

3. We incorporate our scheme and refined GNC-IRLS/RANSAC for further robustness improvement, which outperforms the SOTA framework event at significantly lower iterations.

% Organization of the paper
The paper is organized as follows. Section.~\ref{sec:related works} discusses the related works on two-view robust pose estimation. Section.~\ref{sec:Preliminaries and Notation} presents the used mathematical notations. Section.~\ref{sec:Essential_Equation_and_Its_Properties} comes up with a general solution to the essential equation considering degenerate cases, and shows its relationship to the homography matrix.  A linear relative pose (LiRP) algorithm and the pose-only residuals/pose identification/optimization are proposed in Section.~\ref{sec: LiRP_based_on_PPO}. Section.~\ref{sec: GNC-RANSAC method} elaborates on the combination of LiRP, GNC-IRLS, and RANSAC. Section.~\ref{sec:Experiments} presents both simulation and real data test results. The paper is concluded in Section.~\ref{sec:conclusion}.




\section{Related Works}
\label{sec:related works}


\subsection{Two-view Pose Constraints and Estimation}
\noindent Nowadays, the mainstream methodologies of two-view relative pose estimation, for example in such popular platforms as OpenMVG~\cite{moulon2017openmvg}, COLMAP~\cite{schonberger2016structure} and OpenGV~\cite{kneip2014opengv}, unexceptionally rely on the minimal matching point pairs and the essential matrix equation~\cite{hartley2003multiple,longuet1981computer,stewenius2006recent,kneip2014opengv,moulon2017openmvg}. The advantage of the essential matrix coplanar equation is that it allows for a linear solution to the relative pose, but it loses the depth information and is thus an incomplete representation of two-view geometry~\cite{cai2019equivalent,cai2021pose}. As a result, the relative pose estimation based on the essential matrix equation faces a number of challenges, such as multiple solutions, planar scenes, and pure rotational motion, among others~\cite{nister2004efficient,stewenius2006recent,pizarro2003relative,Philip1998,kneip2012finding,zhao2020efficient}. In 1996, Philip introduced a non-iterative algorithm to determine all essential matrices corresponding to five point pairs~\cite{philip1996non} and proposed a linear method for solving the essential matrix from six point pairs. In 2003, Pizarro et al.~\cite{pizarro2003relative} introduced a six-point algorithm for robustly estimating the essential matrix, which remains effective even in the context of planar scenes. In 2004, Nister~\cite{nister2004efficient} employed the approach of solving the underdetermined group of coplanar equations to analyze the relationship between ternary polynomials and proposed a five-point algorithm for solving the essential matrix. In 2006, Stevenius and Nister~\cite{stewenius2006recent} incorporated the Grobner base theory to further advance the five-point method.

Kneip et al.~\cite{kneip2012finding} believed that the traditional coplanar constraint had issues in expressing pure rotation and then constructed a general coplanar constraint to describe the two-view geometry. Specifically, they utilized the Gröbner basis to solve the relative pose using the five-point method and provided a method to identify the correct pose solution without 3D reconstruction. However, the shortcomings of the coplanar equation (loss of depth information and subject to multiple solutions) were not addressed. In 2014, Kneip et al.~\cite{kneip2014opengv} utilized a distance on the unit sphere manifold \( \mathcal{S}^2 \) (satisfying the chirality constraint) as the objective function for identifying the multiple solutions of relative poses. In 2019, Cai et al.~\cite{cai2019equivalent} introduced a pair of pose-only (PPO) constraints that are equivalent to the two-view imaging relationship and analytically decoupled the camera poses from the 3D scene~\cite{cai2021pose}. They also provided an efficient pose-only identification method (without the requirement of 3D reconstruction) of the four solutions decomposed from the essential matrix.

\subsection{Robust Estimation}
\noindent In 1981, Fischler and Bolles introduced the RANSAC method~\cite{fischler1981random}, which subsequently became the mainstream outlier handling method in SLAM and SfM. The RANSAC method requires empirical parameters to be set according to specific estimation problems, such as the proportion of inliers to outliers and outlier detection error thresholds. Rousseeuw~\cite{rousseeuw1984least} introduced the LMedS method that selects the optimal sub-sample using the minimum median deviation criterion with no need of presetting the threshold to distinguish inliers from outliers. The AC-RANSAC framework by Moisan et al.~\cite{moulon2013adaptive} adaptively updates RANSAC's inlier and outlier parameters. In 2017, Ozyesil et al. pointed out in~\cite{ozyecsil2017survey} that the use of RANSAC could only mitigate the outlier rate to a higher yet acceptable level, for example 40\% in offline applications; however, in real-time applications, the outlier ratio could be as high as 80\% $\sim$ 90\%. Recent advances like MAGSAC/MAGSAC++~\cite{barath2019magsac,barath2020magsac++} have enhanced robustness and accuracy without the need of setting an exact user-defined inlier threshold by marginalizing the $\sigma$-scale. Li et al. ~\cite{li2020gesac} has shown that integrating IRLS with the RANSAC method significantly improves outlier handling.

Furthermore, the robust M-estimator method~\cite{huber1992robust,de2021review} is often employed in nonlinear optimization tasks, such as pose graphs and bundle adjustments. It utilizes a loss function to weight the cost function and aims to obtain robust solutions when faced with outliers or non-normal data distributions. In two-view pose estimation, for example, a recent N-point method~\cite{zhao2020efficient} was proposed with a quasi-convex optimization-based solver for essential matrix estimation using all point pairs. 

Another prevalent method of outlier handling is based on the IRLS~\cite{holland1977robust,huber2004robust}, which reweights the conventional least squares by using the observation residual. For instance, in global rotation averaging, Chatterjee et al.~\cite{chatterjee2017robust} utilized the IRLS scheme to enhance the estimation robustness.  In 2023, Peng et al.~\cite{peng2023convergence} introduced the GNC-IRLS method, a graduated non-convex strategy for IRLS. It proposed a smooth majorizer function and superlinear schedule update rule for IRLS and trimmed Least Squares (TLS) problems, and demonstrated rapid convergence and better accuracy in 3D point registration.

\section{ Preliminaries and Notations}
\label{sec:Preliminaries and Notation}
\noindent In this paper, we use the tilde symbol to denote the bearing vector form of a vector,  i.e., $\boldsymbol{\tilde{a}}=\boldsymbol{a}/\| \boldsymbol{a} \|$, where $\| 
\boldsymbol{a} \|$ denotes the norm of the vector $\boldsymbol{a}$.
The relative pose between two views is represented by $R$ and $\boldsymbol{t}$, respectively. The skew-symmetric matrix formed by any vector $\boldsymbol{a}$ is denoted as $[\boldsymbol{a}]_{\times}$. 

Assume that the left and right views correspond to the camera coordinate systems $C$ and $C^{\prime}$ centered at $c$ and $c'$, respectively. Consider $n$ 3D feature points, where $\boldsymbol{X}_i^C=\left(x_i^C, y_i^C, z_i^C\right)^T$ in the left-camera coordinate system and $\boldsymbol{X}_i^{ C^{\prime}}=\left(x_i^{C^{\prime}}, y_i^{C^{\prime}}, z_i^{C^{\prime}}\right)^T$ in the right one, and the normalized image coordinates of 3D points projected onto the left and right views are $\boldsymbol{x}_i=\boldsymbol{X}_i^C / z_i^C=\left(x_i, y_i, 1\right)^T$ and $\boldsymbol{x}_i^{\prime}=\boldsymbol{X}_i^{C^{\prime}} / z_i^{C^{\prime}}=\left(x_i^{\prime}, y_i^{\prime}, 1\right)^T$,  for $i=1,\ldots ,n$. Here, $z_i^C$ and $z_i^{C^{\prime}}$  are respectively the depths of 3D points in the left and right views~\cite{cai2021pose}. 

%% 新增本质矩阵流形定义：
Define the set
\begin{equation}
\overline{\mathcal{M}}_E \triangleq \left\{E \mid E = [\boldsymbol{t}]_{\times} R, \exists R \in \mathrm{SO}(3), \boldsymbol{t} \in \mathcal{S}^2 \right\},
\end{equation}
where $\mathcal{S}^2$ denotes the two-dimensional spherical manifold. This set is referred to as the standard essential matrix manifold $\overline{\mathcal{M}}_E$ \cite{zhao2020efficient}. Considering the issue of scale, the general essential matrix manifold $\mathcal{M}_E$ can be expressed as
\begin{equation}
\mathcal{M}_E \triangleq \left\{ E \mid E = [\lambda \boldsymbol{t}]_{\times} R, \exists R \in \mathrm{SO}(3), \boldsymbol{t} \in \mathcal{S}^2, \lambda \neq 0 \right\}.
\end{equation}

For any real matrix $Q \in \mathbb{R}^{3 \times 3}$, the necessary and sufficient conditions for $Q$ to belong to $\mathcal{M}_E$ can be described as follows \cite{faugeras1990motion, zhao2020efficient,nister2004efficient,hartley1995investigation}:

\begin{enumerate}

\item \textbf{Condition 1}: The three singular values $\sigma_1 \geq \sigma_2 \geq \sigma_3$ of the matrix $Q$ must satisfy $\sigma_1 = \sigma_2 \neq 0$ and $\sigma_3 = 0$;

\item \textbf{Condition 2}: The matrix $Q$ must satisfy the following two constraints:
    \begin{enumerate}
        \item \textbf{Determinant Constraint}
        \begin{equation}
        \operatorname{det}(Q) = 0;
        \end{equation}
        \item \textbf{Demazure Constraint}
        \begin{equation}
        Q Q^T Q - \frac{1}{2} \operatorname{trace}\left(Q Q^T\right) Q = 0.
        \end{equation}
    \end{enumerate}

        \item \textbf{Condition 3}: There exists a non-zero vector $\boldsymbol{a} \in \mathbb{R}^3$ such that
\begin{equation}
Q Q^T = [\boldsymbol{a}]_{\times} [\boldsymbol{a}]_{\times}^T.
\end{equation}
\end{enumerate}


The above necessary and sufficient conditions for the essential matrix manifold provide three independent constraints on a general $3 \times 3$ real matrix $Q$~\cite{faugeras1990motion,philip1996non}. As the essential matrix itself requires to solve 8 unknowns up to the scale, at least five additional independent constraints are needed to uniquely determine the essential matrix.

\section{Essential Equation and Its Properties}
\label{sec:Essential_Equation_and_Its_Properties}
\subsection{Review of Essential Equation}
\label{sec:review_of_E_eqs}

\noindent The two-view geometry depicts the interrelation between two images of a static scene taken from two viewpoints. It is dictated by multiple different constraints that are crucial to a plethora of computer vision endeavors.

The 3D feature coordinates in the two views are related via their relative pose:
\begin{equation}
\boldsymbol{X}_i^{C^{\prime}} = R \boldsymbol{X}_i^C + \boldsymbol{t}.
\label{eq:eq1}
\end{equation}
Then, the classical two-view imaging equation can be expressed as:
\begin{equation}
z_i^{C^{\prime}} \boldsymbol{x}_i^{\prime} = z_i^C R \boldsymbol{x}_i + \boldsymbol{t} \Leftrightarrow \boldsymbol{x}_i^{\prime} = \lambda_i (R \boldsymbol{x}_i + s_i \boldsymbol{t}),
\label{eq:eq2}
\end{equation}
where $\lambda_i \triangleq z_i^C / z_i^{C^{\prime}} \in \mathbb{R}^{+}$ and $s_i \triangleq 1 / z_i^C \in \mathbb{R}^{+}$ represent unknown depth factors. Left-multiplying the equation on both sides by $\boldsymbol x_i^{\prime T}[\boldsymbol t]_{\times}$ to eliminate the depth factors, then we obtain the Longuet-Higgins's coplanar constraint~\cite{longuet1981computer}:
\begin{equation}
\boldsymbol{0} = \boldsymbol{x}_i^{\prime T}[\boldsymbol{t}]_{\times} \boldsymbol{x}_i^{\prime} = \lambda_i \boldsymbol{x}_i^{\prime T}[\boldsymbol{t}]_{\times} R \boldsymbol{x}_i
\Leftrightarrow \boldsymbol{x}_i^{\prime T} E \boldsymbol{x}_i = 0,
\label{eq:essential equation}
\end{equation}
The derivation from (\ref{eq:eq2}) to (\ref{eq:essential equation}) is irreversible, and it should be highlighted that (\ref{eq:essential equation}) loses partial geometric imaging information, such as the forward intersection of projection rays~\cite{cai2021pose}. In 2012, Kneip et al.~\cite{kneip2012finding} proposed a general coplanar equation to describe the relationship of two-view imaging geometry. It can be alternatively obtained by left-multiplying (\ref{eq:eq2}) by $\boldsymbol{t}^T\left[\boldsymbol{x}_i^{\prime}\right]_\times$, that is:
\begin{equation}
\begin{aligned}
& \boldsymbol{0} = \lambda_i \boldsymbol{t}^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times}(R \boldsymbol{x}_i + s_i \boldsymbol{t}) = \lambda_i \boldsymbol{t}^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times} R \boldsymbol{x}_i \\
& \Leftrightarrow \boldsymbol{t}^T(\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i) = \boldsymbol{0},
\end{aligned}
\label{eq:eq5}
\end{equation}
which means that all vectors $\{\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i | i = 1, ..., n\}$ are located on a plane normal to the relative translation $\boldsymbol t$ . Define $B = (\boldsymbol{x}_1^{\prime} \times R \boldsymbol{x}_1, ..., \boldsymbol{x}_n^{\prime} \times R \boldsymbol{x}_n)^T$ and $M = B B^T$. According to (\ref{eq:eq5}), the $B$ matrix is rank-deficient, i.e., the smallest eigenvalue of the $M$ matrix satisfies the constraint ~\cite{kneip2012finding,kneip2013direct}:
\begin{equation}
\lambda_M(R) = 0,
\label{eq:eq6}
\end{equation}
Again due to the irreversibility of the derivation, the above constraint loses geometric information as well. As a matter of fact, it can be proved that Kneip's equation is equivalent to the essential equation (\ref{eq:essential equation}). 


\begin{proposition} In two-view geometry, Kneip's constraint is equivalent to the essential equation. \end{proposition}
\begin{proof}\\
\noindent a) Sufficiency Proof:
Assume a matrix $B=\left(\boldsymbol{x}_1^{\prime} \times R \boldsymbol{x}_1, \ldots, \boldsymbol{x}_n^{\prime} \times R \boldsymbol{x}_n\right)^T \triangleq\left(\boldsymbol{b}_1, \ldots, \boldsymbol{b}_n\right)^T$, where
\begin{equation}
\boldsymbol{b}_i^T=\operatorname{vec}(R)^T\left(x_i\left[\boldsymbol{x}_i^{\prime}\right]_{\times} \quad y_i\left[\boldsymbol{x}_i^{\prime}\right]_{\times} \quad\left[\boldsymbol{x}_i^{\prime}\right]_{\times}\right)^T.
\label{eq:sup1}
\end{equation}
The operator $\operatorname{vec}()$ denotes the vectorization of a matrix. Let $R=\left(\begin{array}{ccc}\boldsymbol{r}_1 & \boldsymbol{r}_2 & \boldsymbol{r}_3\end{array}\right)$ and substituting it into the above equation

\begin{equation}
\begin{aligned}
\boldsymbol{b}_i^T & =-x_i \boldsymbol{r}_1^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times}-y_i \boldsymbol{r}_2^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times}-\boldsymbol{r}_3^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times} \\
& =\left(\begin{array}{ccc}
x_i \boldsymbol{x}_i^{\prime T} & y_i \boldsymbol{x}_i^{\prime T} & \boldsymbol{x}_i^{\prime T}
\end{array}\right)
\left(\begin{array}{c}
{\left[\boldsymbol{r}_1\right]_{\times}} \\
{\left[\boldsymbol{r}_2\right]_{\times}} \\
{\left[\boldsymbol{r}_3\right]_{\times}}
\end{array}\right) \\
& \triangleq\left(\boldsymbol{x}_i^T \otimes \boldsymbol{x}_i^{\prime T}\right) G \\
& \triangleq \boldsymbol{a}_i^T G.
\label{eq:sup2}
\end{aligned}
\end{equation}
According to Kneip's constraint: only the correct $R$ will cause the matrix $B$ to be rank deficient. That is to say, only the correct rotation matrix $R$ results in a non-zero vector $\boldsymbol{k}=\left(k_1, k_2, k_3\right)^T \in \mathbb{R}^{3 \times 1}$ satisfying
\begin{equation}
\boldsymbol{a}_i^T G \boldsymbol{k}={0}.
\label{eq:sup3}
\end{equation}
Considering the specific form of $G$ matrix, we have
\begin{equation}
-G \boldsymbol{k}=-\left(\begin{array}{c}
{\left[\boldsymbol{r}_1\right]_{\times}} \\
{\left[\boldsymbol{r}_2\right]_{\times}} \\
{\left[\boldsymbol{r}_3\right]_{\times}}
\end{array}\right) \boldsymbol{k}=\left(\begin{array}{c}
{[\boldsymbol{k}]_{\times} \boldsymbol{r}_1} \\
{[\boldsymbol{k}]_{\times} \boldsymbol{r}_2} \\
{[\boldsymbol{k}]_{\times} \boldsymbol{r}_3}
\end{array}\right) \triangleq \operatorname{vec}(Q),
\label{eq:sup4}
\end{equation}
where $Q=\left([\boldsymbol{k}]_{\times} \boldsymbol{r}_1 \quad[\boldsymbol{k}]_{\times} \boldsymbol{r}_2 \quad[\boldsymbol{k}]_{\times} \boldsymbol{r}_3\right)=[\boldsymbol{k}]_{\times} R$. Therefore,
\begin{equation}
\left(\boldsymbol{x}_i^T \otimes \boldsymbol{x}_i^{\prime T}\right) \operatorname{vec}(Q)=\boldsymbol{x}_i^{\prime T}[\boldsymbol{k}]_{\times} R \boldsymbol{x}_i={0}.
\label{eq:sup5}
\end{equation}
Substituting (\ref{eq:eq2}) into the above, we have
\begin{equation}
\left(R \boldsymbol{x}_i+s_i \boldsymbol{t}\right)^T[\boldsymbol{k}]_{\times} R \boldsymbol{x}_i=\boldsymbol{t}^T[\boldsymbol{k}]_{\times} R \boldsymbol{x}_i={0}.
\end{equation}
Since the above equation holds for any normalized image coordinates $\boldsymbol x_i$, it indicates $\boldsymbol k= \boldsymbol t$ (up to a scale), hence $\boldsymbol{x}_i^{\prime T} E \boldsymbol{x}_i={0}$ is valid.


\noindent b)	Necessity Proof: It is evident that the essential equation in (\ref{eq:essential equation}) can be rewritten as a linear constraint with respect to the relative translation, 
\begin{equation} \boldsymbol{x}_i^{\prime T}[\boldsymbol{t}]_{\times} R \boldsymbol{x}_i = \left(-[\boldsymbol{t}]_{\times} \boldsymbol{x}_i^{\prime}\right)^T R \boldsymbol{x}_i = \boldsymbol{t}^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times} R \boldsymbol{x}_i = 0, 
\end{equation} 
which corresponds to  (\ref{eq:eq5}) derived from the Kneip constraint. This establishes a linear constraint on the relative translation, 
\begin{equation} \left(\left[\boldsymbol{x}_1^{\prime}\right]_{\times} R \boldsymbol{x}_1 \quad \cdots \quad \left[\boldsymbol{x}_n^{\prime}\right]_{\times} R \boldsymbol{x}_n\right)^T \boldsymbol{t} = B^T \boldsymbol{t} = 0, 
\end{equation} 
indicating that the matrix $B$ is rank-deficient. Consequently, the solution to the essential equation also satisfies the Kneip constraint in (\ref{eq:eq6}). 
In summary, the Kneip constraint is equivalent to the essential equation. Q.E.D.
\end{proof}


\subsection{Intrinsic Properties of Essential Equation }
\label{sec:IntrinsicPropertyOfE_eqs}
%% 对于Q的定义需要明确

    To avoid confusion between the solution of the essential equation and the true essential matrix $E=[\boldsymbol{t}]_{\times} R$, we denote the solution to the essential equation by $Q$. The essential equation can be written as:
\begin{equation}
    \boldsymbol{x}_i^{\prime T} Q \boldsymbol{x}_i=0 \Rightarrow \left(\boldsymbol{x}_i^T \otimes \boldsymbol{x}_i^{\prime T}\right) \operatorname{vec}(Q) \triangleq \boldsymbol{a}_i^T \boldsymbol{q}=0,
    \label{eq:Q_equation}
\end{equation}
where $A \triangleq \left(\boldsymbol{a}_1, \ldots, \boldsymbol{a}_n\right)^T$ and \( \boldsymbol q \) denotes the vectorized form of the matrix \( Q \) arranged column-wisely. The causes of degeneracy in solving the essential equation include: pure rotational motion, planar structures, specific quadric surfaces, and scenes with all 3D points lying on a straight line. Pure rotational motion induces a well-known singularity phenomenon, in which the essential matrix $E$ degenerates to a zero matrix and the essential equation apparently reduces to the identity $0=0$. However, the matrix $Q$ obtained by solving the essential equation can still lead to the correct relative rotation (see Proposition \ref{prop:pure rotation}). 

\begin{proposition}
In the case of pure rotational motion, $\operatorname{rank}(A)=6$, and the solution $Q$ of the essential equation belongs to $\mathcal{M}_E$.
\label{prop:pure rotation}
\end{proposition}
\begin{proof}
In pure rotational motion, the true relative translation $\boldsymbol{t}=0$. However, the work~\cite{cai2019equivalent} found that the solution matrix $Q = R[\boldsymbol a]_{\times}$ of the essential equation is actually independent of the true relative translation $\boldsymbol{t}$ in this case. Therefore, the undetermined coefficient $\boldsymbol{a}$ cannot be determined from the essential equation, which means $\operatorname{rank}(A) = 8-2 = 6$ (ignoring one degree of freedom on the scale). However, from (\ref{eq:Q_equation}), the matrix $Q$ belongs to $\mathcal{M}_E$ and still maintains a relationship with the true value of the relative rotation $R$.  Q.E.D.
\end{proof}

In planar scene structures, the matching point pairs follow a homography relation $\boldsymbol{x}_i^{\prime} \sim H \boldsymbol{x}_i$  and then the relative pose can be recovered by decomposing the obtained homography matrix $H$~\cite{hartley2003multiple}. It requires us to confirm beforehand whether the 3D scene is planar and otherwise the relative pose estimation thus obtained would be decayed. Therefore, to ensure the robustness of relative pose estimation in engineering applications,  the essential equation and the homography relation are collectively leveraged. Specifically, we need to respectively solve for the essential matrix $\hat{Q}$ and the homography matrix $\hat{H}$ so as to recover all (up to 8) candidate relative poses and then use the reprojection error criterion to select the more appropriate model (essential equation vs. homography relation) and the best relative pose.

In 2003, Pizarro et al. \cite{pizarro2003relative} showed that the six-point algorithm, combined with the Demazure constraint, can in principle handle planar structures, but its performance was shown unstable \cite{nister2004efficient, stewenius2006recent}. To ensure stable essential matrix estimation under planar structures, Nister and Stewenius \cite{stewenius2006recent} proposed the five-point algorithm. In view of the five constraints on the essential equation by five points, the algorithm constructs a four-dimensional solution space using the singular vectors corresponding to the four smallest singular values and combines three independent constraints from the essential matrix manifold $\mathcal{M}_E$ to construct polynomial equations and solve for the essential matrix.

In the three Propositions that follow, we will show that the solution to the essential equation in planar scenes does not belong to the essential matrix manifold anymore but inherently relates to the homography matrix. In this regard, the right relative pose can be identified by merely dealing with the essential equation.

\begin{proposition}
In planar scene structures, $\operatorname{rank}(A)=6$ and the solution $Q$ of the essential equation does not belong to $\mathcal{M}_E$.
\label{proposition:properties of coplanar degeneracy}
\end{proposition}
\begin{proof}
Substituting the two-view imaging equation (\ref{eq:eq2})
into the essential equation (\ref{eq:Q_equation}), we obtain~\cite{cai2019equivalent}
\begin{equation}
\left(\boldsymbol{x}_i^T \otimes\left[\begin{array}{ll}
\boldsymbol{x}_i^T & s_i
\end{array}\right]\right)\left(I_3 \otimes\left(\begin{array}{ll}
R & \boldsymbol{t}
\end{array}\right)^T\right) \boldsymbol{q}=0
\end{equation}
Considering $n$ point pairs, let

\begin{equation}
\small
S \triangleq \left(\begin{array}{c}
\boldsymbol{x}_1^T \otimes\left[\begin{array}{ll}
\boldsymbol{x}_1^T & s_1
\end{array}\right] \\
\vdots \\
\boldsymbol{x}_n^T \otimes\left[\begin{array}{ll}
\boldsymbol{x}_n^T & s_n
\end{array}\right]
\end{array}\right),
\end{equation}

\begin{equation}
\small
\boldsymbol{y} \triangleq\left(I_3 \otimes\left(\begin{array}{ll}
R & \boldsymbol{t}
\end{array}\right)^T\right) \boldsymbol{q},
\end{equation}
then we have
\begin{equation}
S \boldsymbol{y}=0.
\end{equation}
Suppose all 3D points lie on the plane $\boldsymbol{N}^T \boldsymbol{X}_i^C=d$, where $\boldsymbol{N}$ is the normal vector of the plane and $d$ is the distance from the plane to the left camera coordinate center. 
Considering $\boldsymbol{N}^T \boldsymbol{X}_i^C=d$, we have
\begin{equation}
\label{eq:planar structure}
\boldsymbol{N}^T \boldsymbol{x}_i=s_i d \Rightarrow s_i=\frac{\boldsymbol{N}^T \boldsymbol{x}_i}{d}=\boldsymbol{x}_i^T \frac{\boldsymbol{N}}{d}.
\end{equation}
Thus,
\begin{equation}
\resizebox{\linewidth}{!}{$
S=\left(\begin{array}{cccccccccccc}
x_1^2 & x_1 y_1 & x_1 & x_1 \boldsymbol{x}_1^T \frac{\boldsymbol{N}}{d} & y_1 x_1 & y_1^2 & y_1 & y_1 \boldsymbol{x}_1^T \frac{\boldsymbol{N}}{d} & x_1 & y_1 & 1 & \boldsymbol{x}_1^T \frac{\boldsymbol{N}}{d} \\
\vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots \\
x_n^2 & x_n y_n & x_n & x_n \boldsymbol{x}_n^T \frac{\boldsymbol{N}}{d} & y_n x_n & y_n^2 & y_n & y_n \boldsymbol{x}_n^T \frac{\boldsymbol{N}}{d} & x_n & y_n & 1 & \boldsymbol{x}_n^T \frac{\boldsymbol{N}}{d}
\end{array}\right).
$}
\label{eq:S_matrix}
\end{equation}
Clearly, columns 2, 3, 4, 7, 8, and 12 are linearly dependent on other columns, indicating that $\operatorname{rank}(S)=6$ and $\operatorname{rank}(A) \leq \operatorname{rank}(S)=6$. The corresponding specific solutions are:
\begin{equation}
\setlength{\arraycolsep}{2pt} % 调整列间距
\begin{IEEEeqnarraybox}[][c]{rCl}
\boldsymbol{\xi}_1 & = & \left(\begin{array}{@{}llllllllllll@{}}
0 & 1 & 0 & 0 & -1 & 0 & 0 & 0 & 0 & 0 & 0 & 0
\end{array}\right)^T, \\
\boldsymbol{\xi}_2 & = & \left(\begin{array}{@{}llllllllllll@{}}
0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & -1 & 0 & 0 & 0
\end{array}\right)^T, \\
\boldsymbol{\xi}_3 & = & \left(\begin{array}{@{}llllllllllll@{}}
0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & -1 & 0 & 0
\end{array}\right)^T,\\
\boldsymbol{\xi}_4 & = & \left(\begin{array}{@{}llllllllllllll@{}}
\frac{N_1}{d} & \frac{N_2}{d} & \frac{N_3}{d} & -1 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0
\end{array}\right)^T, \\
\boldsymbol{\xi}_5 & = & \left(\begin{array}{@{}llllllllllllll@{}}
0 & 0 & 0 & 0 & \frac{N_1}{d} & \frac{N_2}{d} & \frac{N_3}{d} & -1 & 0 & 0 & 0 & 0
\end{array}\right)^T, \\
\boldsymbol{\xi}_6 & = & \left(\begin{array}{@{}llllllllllllll@{}}
0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & \frac{N_1}{d} & \frac{N_2}{d} & \frac{N_3}{d} & -1
\end{array}\right)^T,
\end{IEEEeqnarraybox}
\end{equation}
where $\boldsymbol{N}=(N_1,N_2,N_3)^T$. Thus,
\begin{equation} \begin{aligned} \boldsymbol{y} &= \left(I_3 \otimes\left(\begin{array}{ll} R & \boldsymbol{t} \end{array}\right)^T\right) \operatorname{vec}(Q) \\ &= a_1 \boldsymbol{\xi}_1 + a_2 \boldsymbol{\xi}_2 + a_3 \boldsymbol{\xi}_3 + b_1 \boldsymbol{\xi}_4 + b_2 \boldsymbol{\xi}_5 + b_3 \boldsymbol{\xi}_6 ,\end{aligned} \end{equation}
in which $a_i$ and $b_i (i=1,2,3)$ are undetermined coefficients. Rearranging, we obtain:
\begin{equation}
\small % 减小字体大小
\setlength\arraycolsep{2pt} % 减小矩阵列之间的间距
\binom{R^T Q}{\boldsymbol{t}^T Q}=\left(\begin{array}{ccc}
b_1 \frac{N_1}{d} & -a_1+b_2 \frac{N_1}{d} & -a_2+b_3 \frac{N_1}{d} \\
a_1+b_1 \frac{N_2}{d} & b_2 \frac{N_2}{d} & -a_3+b_3 \frac{N_2}{d} \\
a_2+b_1 \frac{N_3}{d} & a_3+b_2 \frac{N_3}{d} & b_3 \frac{N_3}{d} \\
-b_1 & -b_2 & -b_3
\end{array}\right).
\end{equation}
This can be split into two key equations:
\begin{equation}
Q=R\left([\boldsymbol{a}]_{\times}+\frac{\boldsymbol{N} \boldsymbol{b}^T}{d}\right)
\label{eq:QRax}
\end{equation}
and
\begin{equation}
\boldsymbol{t}^T Q=-\boldsymbol{b}^T,
\label{eq:tQb}
\end{equation}
where $\boldsymbol{a}=\left(a_1,-a_2, a_3\right)^T$ and $\boldsymbol b=(b_1,b_2,b_3)^T$. For planar structures,  (\ref{eq:QRax}) shows that the solution $Q$ from the essential equation does not belong to $\mathcal{M}_E$. Substituting  (\ref{eq:tQb}) into (\ref{eq:QRax}), we get:
\begin{equation}
\small % 减小字体大小
\begin{aligned}
Q &= R\left([\boldsymbol{a}]_{\times} - \frac{\boldsymbol{N} \boldsymbol{t}^T}{d} Q\right) \\
& \Leftrightarrow \left(I_3 + R \frac{\boldsymbol{N} \boldsymbol{t}^T}{d}\right) Q = R[\boldsymbol{a}]_{\times}.
\end{aligned}
\end{equation}
From this, it can be seen that, similar to pure rotation degeneracy, the three coefficients $\boldsymbol{a}$ in the undetermined coefficients cannot be determined by the essential equation, meaning $\operatorname{rank}(A)=6$. Q.E.D.
\end{proof}

\\
\begin{proposition}
\label{proposition:relationship between Q and H}
In planar scene structures, the solution $Q$ of the essential equation relates to the homography matrix $H$ by
\begin{equation}
\label{eq:relationQH}
Q^T H + H^T Q = 0.
\end{equation}
\end{proposition}

\begin{proof}
In planar structures, substituting (\ref{eq:planar structure}) into the classic two-view imaging equation, we obtain the homography relation:
\begin{equation}
\boldsymbol{x}_i^{\prime} \sim \left(R + \frac{\boldsymbol{t} \boldsymbol{N}^T}{d}\right) \boldsymbol{x}_i \triangleq H \boldsymbol{x}_i.
\end{equation}
Combining equations (\ref{eq:QRax})  and (\ref{eq:tQb}), we have:
\begin{equation}
\small % 减小字体大小
\begin{aligned}
H^T Q &= \left(R^T + \frac{\boldsymbol{N} \boldsymbol{t}^T}{d}\right) Q \\
&= R^T Q - \frac{\boldsymbol{N} \boldsymbol{b}^T}{d} = [\boldsymbol{a}]_{\times}.
\end{aligned}
\end{equation}
Therefore, $Q^T H + H^T Q = 0$. Q.E.D.
\end{proof}\\

\\
\begin{proposition}
\label{proposition:linear representation of H}
In planar structures, the homography matrix $H$ can be linearly recovered by the three solutions $Q_1, Q_2$, and $Q_3$ corresponding to the three smallest singular values of the coefficient matrix $A$ of the essential equation.
\end{proposition}

\begin{proof}
According to Proposition \ref{proposition:properties of coplanar degeneracy}, in planar structures, $\operatorname{rank}(A) = 6$, and the solution of the essential equation has the forms (\ref{eq:QRax})  and (\ref{eq:tQb}). Therefore, taking the three solutions $Q_1, Q_2$, and $Q_3$ corresponding to the three smallest singular values, according to Proposition \ref{proposition:relationship between Q and H}, there exists the relationship with the homography matrix $H$:
$$Q_i^T H + H^T Q_i = 0 $$
\begin{equation} 
\label{eq:solveH} 
\Leftrightarrow \left[\left(I_3 \otimes Q_i\right) + \left(Q_i^T \otimes I_3\right) C_{3,3}\right] \operatorname{vec}(H) = 0 ,
\end{equation}
where $C_{3,3}$ is a $9 \times 9$ permutation matrix. Considering that the singular vectors corresponding to the three smallest singular values of the matrix $A$ in the SVD decomposition are orthogonal to each other, the matrices $Q_1, Q_2$, and $Q_3$ are nonlinearly related. Additionally, considering that $H^T Q_i$ is an antisymmetric matrix (up to a scale), each $Q_i$ provides $6 - 1 = 5$ constraints on the matrix $H$. In summary, using (\ref{eq:solveH}) and the matrices $Q_1, Q_2$, and $Q_3$, the homography matrix $H$ can be directly linearly solved. Q.E.D.
\end{proof}

Proposition~\ref{proposition:linear representation of H} proves that even in planar scenarios, the particular solutions \( Q_1, Q_2, Q_3 \), which correspond to the singular vectors associated with the three smallest singular values of the essential equation, maintain the relationship with the homography matrix, as indicated in (\ref{eq:solveH}). This relationship is consistent with the relationship between the truth essential matrix and the truth homography matrix \cite{ma2004invitation}. Given that \( Q_1, Q_2 \), and \( Q_3 \) are linearly independent, the homography matrix can be directly computed instead of utilizing the homography relationship.


\section{Linear Relative Pose Estimation Based on Pose-only Constraints}
\label{sec: LiRP_based_on_PPO}


\subsection{Linear Solver of Essential Matrix for N-point Problem}
\label{sec:LiRP}
\noindent According to (\ref{eq:Q_equation}), we have $A \boldsymbol q=0$. Assume $\sigma_{1} \geq \sigma_{2} \geq \sigma_{3}$ are the three smallest singular values of matrix $A$, with their corresponding singular vectors \(\boldsymbol q_1\), \(\boldsymbol q_2\), and \(\boldsymbol q_3\). As $rank(A) = 6$ in the planar scene or pure rotational motion, the true solution of  \( \boldsymbol q \) will be in the three-dimensional solution space spanned by \(\boldsymbol q_1\), \(\boldsymbol q_2\), and \(\boldsymbol q_3\), i.e.,
\begin{equation}
\boldsymbol q = a \boldsymbol q_1 + b \boldsymbol q_2 + c \boldsymbol q_3,
\end{equation}
where $a$, $b$, and $c$ are coefficients to be determined. The general solution $\boldsymbol q$ is determined up to a scale. According to Proposition~\ref{proposition:relationship between Q and H}, it can be easily verified that for any $\boldsymbol q$ corresponding to a $Q$ matrix, there also exists a relationship with the homography matrix $H$ as given in (\ref{eq:relationQH}). Subsequently, the determination of these coefficients will be discussed in two cases:

Case (1): If c~=0, then let $c = 1$. Then the values of $a$ and $b$ can be determined such that \( Q \) is an essential matrix satisfying the Demazure constraint~\cite{faugeras1993three}:
\begin{equation}
Q Q^{T} Q - \frac{1}{2} \text{trace}(Q Q^{T}) Q = 0
\Leftrightarrow D \boldsymbol y=0,
\label{eq:Dy=0}
\end{equation}
where $D$ is a 9x10 matrix constructed from \(\boldsymbol q_1\), \(\boldsymbol q_2\), and \(\boldsymbol q_3\), and $\boldsymbol y =(a^3, a^2b, ab^2, b^3, a^2, ab, b^2, a, b, 1)^T$. Suppose that the first four columns form a submatrix $D_1$ and the remaining columns constitute a submatrix $D_2$, that is, $D=(D_1|D_2)$. By multiplying (\ref{eq:Dy=0}) to the left with the pseudo-inverse of $D_1$, denoted as $D_1^+$, we obtain
\begin{equation}
D_1^{\dagger} D \boldsymbol{y}=\left(I_{4 \times 4} \mid M_{4 \times 6}\right) \boldsymbol{y}=0.
\end{equation}
The structure on the left side of the above equation can be illustrated as in Table~\ref{table1}, of which the first row lists the monomial elements of $\boldsymbol y$, the horizontal bars "-" represent specific values of \(M\), and other blank entries are 0s. In particular, each monomial of third order in $\boldsymbol y$ can be expressed as linear combinations of those of lower orders in the same row. In this regard, $(I | M) \boldsymbol y=0$ gives rise to the equations \(C_1\boldsymbol g = a\boldsymbol{g}\) and \(C_2\boldsymbol g = b\boldsymbol{g}\) of eigensystem form, where \(\boldsymbol g = (a^2, ab, b^2, a, b, 1)^T\). Specifically, suppose the vector $\boldsymbol{M}_i^T$ represents the $i$-th row vector of the matrix $M$, and the matrices $F_1$ and $F_2$ are defined as follows:
\begin{equation}
\begin{aligned}
& C_1=\left(\begin{array}{llllll}
-\boldsymbol{M}_1 & -\boldsymbol{M}_2 & -\boldsymbol{M}_3 & \boldsymbol{e}_1 & \boldsymbol{e}_2 & \boldsymbol{e}_4
\end{array}\right)^T \\
& C_2=\left(\begin{array}{llllll}
-\boldsymbol{M}_2 & -\boldsymbol{M}_3 & -\boldsymbol{M}_4 & \boldsymbol{e}_2 & \boldsymbol{e}_3 & \boldsymbol{e}_5
\end{array}\right)^T,
\end{aligned}
\end{equation}
where the vector $\boldsymbol{e}_i$ represents the $i$-th column vector of the 6-dimensional identity matrix. Therefore, by determining the eigenvectors of $6$-by-$6$ action matrices \(C_1\) and \(C_2\), as many as 12 potential solution vectors for \(\boldsymbol g\) can be derived. The coefficients \(a\) and \(b\) can be directly determined from the last three elements of each solution vector, specifically, \(a = g_4/g_6\) and \(b = g_5/g_6\). In this scenario, we obtain 12 candidate solutions for $\boldsymbol q$, denoted as $\boldsymbol q_s$.

\begin{table}[h]
    \centering
    \caption{Polynomial system.}
    \label{table1}
    \begin{tabular}{|c|c|c|c|c|c|c|c|c|c|}
        \hline
        $a^3$ & $a^2b$ & $ab^2$ & $b^3$ & $a^2$ & $ab$ & $b^2$ & $a$ & $b$ & $1$ \\
        \hline
        1 & & & & - & - & - & - & - & - \\
        \hline
        & 1 & & & - & - & - & - & - & - \\
        \hline
        & & 1 & & - & - & - & - & - & - \\
        \hline
        & & & 1 & - & - & - & - & - & - \\
        \hline
    \end{tabular}
\end{table}

Case (2). If $c = 0$, then we deduce $\boldsymbol q = a \boldsymbol q_1 + b \boldsymbol q_2$. Analogously, setting $b = 1$ and  only $a$ needs to be determined. Considering the property that the determinant of the essential matrix is zero, a polynomial in terms of \(a\) can be constructed by
\begin{equation}
det(Q)=0 \Leftrightarrow \boldsymbol d^T \boldsymbol z=0,
\label{eq:Dz=0}
\end{equation}
where  $\boldsymbol d$ is a three-dimesional coefficient vector and  \(\boldsymbol z = (a^3, a^2, a, 1)^T\) . In analogy to Case (1), three candidate solutions for $\boldsymbol q$ can be derived, say $\boldsymbol q^\prime_s$. Setting $b = 0$, $\boldsymbol q_1$ also emerges as a potential solution. Taking into account different solution sequences, $\boldsymbol q_2$ and $\boldsymbol q_3$ can similarly be considered as candidate solutions.

In summary, the above analysis yields 18 candidate solutions for $\hat{\boldsymbol q} = (\boldsymbol q_s,\boldsymbol q^\prime_s, \boldsymbol q_1,\boldsymbol q_2,\boldsymbol q_3 )$. Each candidate solution can be decomposed into four relative pose solutions~\cite{hartley2003multiple}. With the aid of the relative pose identification's inequality strategy revealed in~\cite{cai2019equivalent}, that is to say, $M_1(R)>0$ and $M_2(R,\boldsymbol t)>0$ therein, 18 candidate solutions of the essential matrix can be processed to yield the same number of candidate relative poses without 3D reconstruction. 

\begin{algorithm}[htbp]
\caption{Weighted LiRP for Pose Estimation}\label{alg:LiRP algorithm}
\begin{algorithmic}[1]
\Require
  point pairs $\left\{ (\tilde{\boldsymbol x}_1, \tilde{\boldsymbol x}^{\prime}_1),...,(\tilde{\boldsymbol x}_n, \tilde{\boldsymbol x}^{\prime}_n) \right\}$,
  (optional) weights $\boldsymbol w$ 
\Ensure
relative pose $R$, $\boldsymbol t$
\State construct weighted $A$ matrix $\gets $ $A_i = w_i(\tilde{\boldsymbol x}_i^T \otimes \tilde{\boldsymbol x}_i^{\prime T})$
\State obtain $\boldsymbol q_1,\boldsymbol q_2,\boldsymbol q_3 \gets $ SVD decomposition of A matrix
\State compute $D$ matrix by (\ref{eq:Dy=0})
\State obtain $M$ matrix by left multiplying $D_1^+D$
\State construct $C_1$, $C_2$ $\gets M$
\State $\boldsymbol q_s \gets $ eigenvectors of $C_1$, $C_2$
\State $\boldsymbol q^\prime_s \gets $ solve polynomial by (\ref{eq:Dz=0})
\State $\hat{\boldsymbol q} \gets$ $\boldsymbol q_s,\boldsymbol q^\prime_s, \boldsymbol q_1,\boldsymbol q_2,\boldsymbol q_3 $
\State $R, \boldsymbol t \gets $$\hat{\boldsymbol q}$
\end{algorithmic}
\end{algorithm}

\subsection{Pose-only Residuals and Pose Identification}
\label{sec:idm}
Recent studies~\cite{cai2019equivalent,cai2021pose} proposed the pose-only imaging geometry by representing the depth factors as functions of poses, which is provably equivalent to the classical multi-view geometry. According to~\cite{cai2019equivalent}, the pairwise pose-only (PPO) constraint is given as
\begin{equation}
\frac{\left\|\boldsymbol{t} \times R \boldsymbol{x}_i\right\|}{\left\|\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i\right\|} \boldsymbol{x}_i^{\prime}=\frac{\left\|\boldsymbol{t} \times \boldsymbol{x}_i^{\prime}\right\|}{\left\|\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i\right\|} R \boldsymbol{x}_i+\boldsymbol{t}.
\label{eq:eq7}
\end{equation}
The depth factors are linearly related to translation as revealed in ~\cite{cai2021pose}, namely, 
\begin{equation}
\begin{aligned}
& \frac{\left\|\boldsymbol{t} \times \boldsymbol{x}_i^{\prime}\right\|}{\left\|\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i\right\|}=\frac{\left(\left[R \boldsymbol{x}_i\right]_{\times} \boldsymbol{x}_i^{\prime}\right)^T\left[\boldsymbol{x}_i^{\prime}\right]_{\times}}{\theta_i^2} \boldsymbol{t} \triangleq \frac{1}{\theta_i^2} \boldsymbol{h}_i^T \boldsymbol{t}, \\
& \frac{\left\|\boldsymbol{t} \times R \boldsymbol{x}_i\right\|}{\left\|\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i\right\|}=\frac{\left(\left[R \boldsymbol{x}_i\right]_{\times} \boldsymbol{x}_i^{\prime}\right)^T\left[R \boldsymbol{x}_i\right]_{\times}}{\theta_i^2} \boldsymbol{t} \triangleq \frac{1}{\theta_i^2} \boldsymbol{h}_i^{\prime T} \boldsymbol{t},
\end{aligned}
\end{equation}
where $\theta_i=\left\|\boldsymbol{x}_i^{\prime} \times R \boldsymbol{x}_i\right\|$. Following a similar derivation process as in~\cite{cai2021pose} for the LiGT constraint and substituting into (\ref{eq:eq7}) and left-multiplying by $[\boldsymbol{x}_i^{\prime}]_{\times}$, a linear relative translation (LiRT) constraint can be obtained as

\begin{figure*}[!htb]
    \centering
    \includegraphics[width= \textwidth]{images/framework.png}
    \caption{
    Sketch of our LiRP-centered outlier handling framework. The yellow blocks denote the modified module in this paper.
    }
    \label{fig:framework}
\end{figure*}

\begin{equation}
\left([\boldsymbol{x}_i^{\prime}]_{\times} R \boldsymbol{x}_i \boldsymbol{h}_i^T+\theta_i^2 [\boldsymbol{x}_i^{\prime}]_{\times}\right) \boldsymbol{t} \triangleq L_i(R,\boldsymbol{x}_i,\boldsymbol{x}_i^{\prime}) \boldsymbol{t}=0.
\label{eq:eq8}
\end{equation}
As seen from the above derivation from (\ref{eq:essential equation}) to (\ref{eq:eq5}), both PPO and LiRT constraints encompass the coplanar essential matrix equation and the Kneip constraint. With these imaging constraints, two pose-only residuals can be readily formulated, i.e., 
\begin{equation}
\begin{aligned}
v_i^{LiRT}(R, \boldsymbol t,\tilde{ \boldsymbol x}_i,\tilde{\boldsymbol x}^{\prime}_i) &=||\tilde L_i \boldsymbol{t}|| \triangleq ||\boldsymbol \epsilon_i^{LiRT}||, \\
v_i^{PPO}(R, \boldsymbol t, \tilde{\boldsymbol x}_i,\tilde{\boldsymbol x}^{\prime}_i) & =||\tilde{\boldsymbol y}_i - \tilde{\boldsymbol x}_i^{\prime}|| \triangleq ||\boldsymbol \epsilon_i^{PPO}||,
\label{eq:v_LiGT}
\end{aligned}
\end{equation}
where ${\boldsymbol y}_i=\left\|\boldsymbol{t} \times \tilde{\boldsymbol x}_i^{\prime}\right\| R \tilde{\boldsymbol x}_i+\left\|\tilde{\boldsymbol x}_i^{\prime} \times R \tilde{\boldsymbol x}_i\right\| \boldsymbol{t}$ and $\boldsymbol \epsilon$ denotes the corresponding error vector. 

As the PPO constraint has been proven to be equivalent to the two-view geometry~\cite{cai2019equivalent}, we choose the best one among 18 candidate relative poses by 
\begin{equation}
[\hat{R}  | \hat{\boldsymbol{t}}]=\arg \min_{[{R}_{j}|{\boldsymbol{t}}_{j}]} \sum{v_i}^{PPO}(R_j, \boldsymbol t_j, \tilde{\boldsymbol x}_i,\tilde{\boldsymbol x}^{\prime}_i).
\label{}
\end{equation}
Note that the above pose solution identification does not require 3D reconstruction either. Algorithm.\ref{alg:LiRP algorithm} summarizes the key steps of LiRP estimation.

Additionally, a  pose-only optimization can be performed by 
\begin{equation}
argmin_{(R, \boldsymbol{t})}\sum_i{v_i}.
\label{}
\end{equation}

Note that the SOTA methods mostly utilize the coplanar constraint equation (\ref{eq:essential equation}) to form residuals and identify solutions by the Sampson distance~\cite{barath2019magsac,hartley2003multiple,zhao2020efficient}. However, according to Proposition~\ref{proposition:properties of coplanar degeneracy}, the rank of $A$  degenerates to 6 for planar structures, which implies that any matrix constructed from the solution vector space satisfies the essential equation and has zero Sampson distance.

%Unless otherwise specified, the residual vector formed by all point pairs will be denoted as a vector $\boldsymbol v$. 

An exception is the OpenGV platform~\cite{kneip2014opengv}, which constructs the reprojection residual on the 2D spherical manifold $\mathcal{S}^2$ by using 3D feature point reconstruction from all candidate pose solutions.\\




\section{LiRP-centered Outlier Handling}
\label{sec: GNC-RANSAC method}
In order to enhance the LiRP's robustness to outliers, we incorporate the strategies of GNC-IRLS and RANSAC, as illustrated in Fig.~\ref{fig:framework}. Specifically, by integrating the GNC-IRLS with the LiRP algorithm, we have developed a robust relative pose estimation method. However, for higher outlier fractions, the RANSAC scheme is further used to improve robustness. Since the LiRP algorithm solves the N-point problem with some degree of outlier tolerance, exploring a RANSAC-like method that combines LiRP and MS-TLS (Majorization and Superlinear TLS) is worthwhile. This method does not require all sub-samples to consist of only inliers and select with a minimal sample size, compared to the standard RANSAC method. In this regard, our robust relative pose estimation algorithm for fitting the model relaxes the requirement of sub-samples.


\subsection{LiRP-centered GNC-IRLS}
\noindent The recent GNC-based IRLS method~\cite{peng2023convergence} alternates between optimizing a smooth majorizer function and increasing a parameter $\mu$ at each iteration, which is designed to accelerate convergence and yield stable outcomes. The majorizer function, $q(\epsilon, \mu)$, represents a shifted version of the objective function $\rho(\epsilon)$, which is to be minimized. The parameter $\mu$ controls the tradeoff between accuracy and robustness. By alternately updating the majorizer function and increasing $\mu$, GNC~\cite{blake1987visual} aims to find a stationary point of the objective function to reach convergence. 

Let the loss function of TLS be $\rho(\epsilon)=\min \left\{\epsilon^2, \alpha^2\right\}$, Peng et al. provided a majorized loss function \cite{peng2023convergence} as
\begin{equation}
\setlength\arraycolsep{2pt}
\rho(\epsilon, \mu)=\begin{cases}
\epsilon^2,  \text{if } |\epsilon| \leq \alpha, \\
\frac{\mu+1}{\mu} \alpha^2,  \text{if } |\epsilon| \geq \frac{\mu+1}{\mu} \alpha, \\
-\mu \epsilon^2 + 2(1+\mu) \alpha |\epsilon| - (1+\mu) \alpha^2,  \text{o/w}.
\end{cases}
\end{equation}
where $\alpha$ is the truncation parameter. The above majorized loss function  is related to the TLS loss function as $\rho(\epsilon) = \lim_{\mu \rightarrow \infty} \rho(\epsilon, \mu)$. Based on the relationship between the loss function and the weight function, the majorized weight function $w(\epsilon, \mu)$ can be obtained as
\begin{equation}
w(\epsilon, \mu) = \begin{cases}
1, & \text{if } |\epsilon| \leq \alpha \\
0, & \text{if } |\epsilon| \geq \frac{\mu+1}{\mu} \alpha \\
\frac{\alpha(1+\mu)}{\epsilon} - \mu, & \text{o/w}
\end{cases}
\label{eq:weight}
\end{equation}
In other words, by increasing $\mu$ at each iteration to adjust the shape of $\rho(\epsilon, \mu)$, the process eventually converges to a stable point of the original objective function $\rho(\epsilon)$. To increase the convergence speed, the adjustment parameter $\mu$ can be updated superlinearly, such as
\begin{equation}
\mu \leftarrow \begin{cases}
\lambda \sqrt{\mu}, & \mu \leq 1 \\
\lambda \mu, & \mu > 1
\end{cases}, \quad \lambda > 1.
\label{eq:superlinear_update}
\end{equation}
A GNC-based IRLS scheme is referred as the MS-TLS scheme when combined with the majorized TLS loss/weight function and the superlinear update adjustment parameter scheme \cite{peng2023convergence}, as shown in Fig.~\ref{fig:ms_tls_figs}.

In general IRLS problems, the scale-invariant issue is often considered. Let $\sigma$ denote the standard deviation of the sample. Based on Huber's work~\cite{huber2004robust}, we use the median absolute deviation to determine $\sigma$. Since visual geometric constraints ideally have no systematic bias, we assume the errors are subject to the zero-mean Gaussian distribution in this paper. Then, the visual residual statistics can be regarded as the Chi-square distribution with different freedoms. Finally, we incorporated our LiRP method into the GNC-IRLS scheme, as shown in Algorithm.~\ref{alg:IRLS-Pose-only}.



\begin{figure}
    \centering
    \subfloat[]{%
        \includegraphics[width=0.5\linewidth]{images/loss_function.png}
       \label{fig:ms_tls_loss}}
    \subfloat[]{%
        \includegraphics[width=0.5\linewidth]{images/weight_function.png}
        \label{fig:ms_tls_weight}}
    \caption{The MS-TLS loss function and corresponding weight function for different values of $\mu$ with $\alpha = 3$. (a) The MS-TLS loss function $\rho(\epsilon, \mu)$ for various values of $\mu$. As $\mu$ increases, the loss function approaches the original TLS loss function. (b) The corresponding weight function $w(\epsilon, \mu)$ for the same values of $\mu$. The weight function adjusts to balance between robustness and accuracy as $\mu$ increases.}
    \label{fig:ms_tls_figs}
\end{figure}



\begin{algorithm}[htbp]
\caption{LiRP-centered GNC-IRLS}\label{alg:IRLS-Pose-only}
\begin{algorithmic}[1]
\Require
  point pairs $\left\{ (\tilde{\boldsymbol x}_1, \tilde{\boldsymbol x}^{\prime}_1), \ldots, 
  (\tilde{\boldsymbol x}_n, \tilde{\boldsymbol x}^{\prime}_n) \right\}$, stop threshold $\epsilon_0$, 
  maximum iteration $n_{iter}$
\Ensure
  relative pose $R, \boldsymbol t$

\State initialize $\epsilon^{(0)}$, $\mu^{0}$, and weights $\boldsymbol w^{(0)}$,
\State $converged \gets \text{false}$

\vspace{0.1cm}
\For{$k \gets 0$ to $n_{iter}-1$}
    \State obtain $R^{(k)}$, $\boldsymbol t^{(k)}$ with $\boldsymbol w^{(k)}$ by Algorithm.~\ref{alg:LiRP algorithm}
    \State compute residual vector $\boldsymbol{v}$ by (\ref{eq:v_LiGT}) 
    \State $\sigma^{(k)} \gets 1.4826 \cdot \text{median}(|\boldsymbol v|)$
    \State $c^{(k)} \gets 5.54 \cdot \sigma^{(k)}$
    \State $\boldsymbol w^{(k+1)} \gets$ update weights by $\boldsymbol{v}$, 
           $c^{(k)}$, and $\mu^{(k)}$ according to~(\ref{eq:weight})
    \State $\epsilon^{(k+1)} \gets \sum w_i^{(k+1)}{v}_i$
\vspace{0.1cm}
    \If{$|\epsilon^{(k+1)} - \epsilon^{(k)}| < \epsilon_0$} 
        \State $converged  \gets \text{true}$
        \State \textbf{exit loop} 
    \EndIf
    \State update $\mu^{(k+1)}$ by super-linear schedule~(\ref{eq:superlinear_update})
\EndFor
\vspace{0.1cm}
\end{algorithmic}
\end{algorithm}


% Table \ref{table: Niter  of GNC-RANSAC}
\begin{table*}[!htbp]
\caption{Number of Iterations of the GNC-RANSAC Scheme for Different Outlier Ratios and Sample Sizes}
\label{table: Niter  of GNC-RANSAC}
\centering
\begin{tabular}{c|c|c|c|c|c|c|c|c}
\hline \hline 
$\gamma$ & $n_s=6$ & $n_s=7$ & $n_s=8$ & $n_s=9$ & $n_s=10$ & $n_s=20$ & $n_s=30$ & \text{RANSAC} \\ 
 &  &  &  &  &  &  &  & ($n_s=5$) \\ 
\hline 
50\% & 344 & 81 & 32 & 17 & 10 & 6 & 6 & 146 \\
60\% & 1428 & 294 & 105 & 50 & 28 & 19 & 33 & 448 \\
70\% & 9243 & 1689 & 540 & 230 & 117 & 148 & 920 & 1893 \\
80\% & 141629 & 23192 & 6651 & 2545 & 1169 & 7110 & 1223443 & 14389 \\
\hline \hline
\end{tabular}
\end{table*}



\begin{algorithm}[htbp]
\caption{LiRP-centered GNC-RANSAC}\label{alg:Gnc-ransac}
\begin{algorithmic}[1]
\Require
  point pairs $P=\left\{ (\tilde{\boldsymbol x}_1, \tilde{\boldsymbol x}^{\prime}_1),...,(\tilde{\boldsymbol x}_n, \tilde{\boldsymbol x}^{\prime}_n) \right\}$, stop threshold $\epsilon_0$, sample size $n_{s}$, $n_{iter}$, inlier threshold $\theta$
\Ensure
relative pose $R, \boldsymbol t$

\State $n_{maxinliers} \gets 0$
; $R_{best}, \boldsymbol t_{best},\gets \emptyset$ 
\For{$k \gets 0$ to $n_{iter}-1$}
\State $ P_s \gets$ randomly select $n_s$ sample from $P$
\State $R_s, \boldsymbol t_s \gets$ fit model using Algorithm.\ref{alg:IRLS-Pose-only} with $P_s$
\State $\boldsymbol v^{PPO} \gets$ obtain residual vector by (\ref{eq:v_LiGT}) for $P$
\State $P_{inliers} \gets$ $\boldsymbol v^{PPO}$, $\theta$

\If{$|P_{inliers}| > n_{maxinliers}$}

\State $n_{maxinliers} \gets |P_{inliers}|$
\State $R_{best}, \boldsymbol t_{best} \gets R_s, \boldsymbol t_s$
\State $P_{best} \gets P_{inliers}$
\EndIf
\EndFor
\State $R, \boldsymbol t \gets$ fit model using Algorithm.~\ref{alg:IRLS-Pose-only} with $P_{best}$

\end{algorithmic}
\end{algorithm}



\subsection{LiRP-centered GNC-RANSAC}
Since we replace the model estimation part in the standard RANSAC framework with GNC-IRLS ( as shown in Algorithm.~\ref{alg:Gnc-ransac}), the adaptive maximum iteration needs to be re-derived.

Assuming there are $n_o$ outliers among total  $n_s$ samples. The number of outliers $X$ that is drawn by the RANSAC method is subject to a hypergeometric distribution $H\left(n_s, n_o, n\right)$. Then, the probability of having at most $k$ outliers in a single draw of $n_s$ samples is
\begin{equation}
\operatorname{CDF}\left(k ; n_s, n_o, n\right)=P(X \leq k)=\sum_{i=0}^k \frac{\binom{n_o}{i} \binom{n-n_o}{n_s-i}}{\binom{n}{n_s}}.
\end{equation}
Therefore, the number of iterations $N$ required to achieve a confidence level $\tau$ is given by
\begin{equation}
N=\frac{\log (1-\tau)}{\log \left(1-\operatorname{CDF}\left(k ; n_s, n_o, n\right)\right)}.
\end{equation}
Let $\kappa$ represent the outlier tolerance of $n_s$ samples, that is to say, the correct model can be obtained for an outlier fraction below $\kappa$. For example, experiment results show that for $n_s=20$, the LiRP method combining MS-TLS has an outlier tolerance $\kappa=50\%$. The outlier tolerance $\kappa$ varies when the GNC-IRLS algorithm is used with different sample sizes. Additionally, the maximum outlier tolerance $\kappa_{\max}$ of $n_s$ samples should satisfy
\begin{equation}
\kappa_{\max}=\left(1-\frac{n_{\min}}{n_s}\right) \times 100\%,
\end{equation}
where $n_{\min}$ represents the minimum number of samples required for model solving. Thus, a reasonable value for $k$ is $k=\left[\min \left\{\kappa_{\max}, \kappa\right\} n_s\right]$ (where $[\cdot]$ denotes rounding down). For instance, with $n=100$ and an outlier tolerance $\kappa=50\%$, the LiRP algorithm requires $n_{\text{min}}=6$ for model solving. Table \ref{table: Niter  of GNC-RANSAC} shows the minimum number of iterations needed for different outlier ratios $\gamma$ and different sample sizes $n_s$ at a confidence level of $\tau=99\%$. It can be observed that the GNC-RANSAC scheme requires significantly fewer iterations than the standard RANSAC method $\left(n_s=5\right)$. 


\begin{figure*}[!htbp]
    \centering
    \includegraphics[width=\textwidth]{images/structure.png}
    \caption{
    Illustration of 3D scenes and motions. 
    Red, green, and blue arrows indicate the X, Y, and Z axes, respectively. 
    Black dots represent 3D points, and green squares highlight visible 3D points satisfying the chirality constraint. 
    Red and blue cameras depict the left and right view camera poses, respectively. 
    The gray box represents the bounding box.
    }
    \label{fig:RTX}
\end{figure*}

\section{Experiments}
\label{sec:Experiments}

\noindent The global translation has been well solved by the LiGT algorithm that is linear and capable of producing nearly optimal global translation given high-quality input rotations~\cite{cai2021pose}. In contrast to the traditional methods~\cite{pizarro2003relative,nister2004efficient,stewenius2006recent,zhao2020efficient} primarily emphasizing the accuracy and robustness of relative translation, the current work focuses on improving the performance of relative rotation estimation.

In both simulation and real data tests, the sample size for GNC-RANSAC is fixed to $n_{s}=20$. Let $\hat{R}_{i,j}$ denote the estimated relative rotations between the i-th and j-th views. Then, the angular error between the estimates and the truth relative rotations $R_{ij}$ can be quantified by
\begin{equation}
\epsilon_{i,j}^{\hat{R}} = \arccos \left(\frac{\operatorname{trace}\left({R}_{ {i,j}}^{\top} {\hat R_{i,j}}\right) - 1}{2}\right).
\end{equation}

\begin{table*}[!htbp]
\centering
\caption{Structure degenerate problems for relative rotation estimation. Each pair (x,y) represents 'x' as the mean rotation error (degree) and 'y' as the minimum distance $d_{\min}$ between the eigenvalues of the action matrix. The symbol ‘-’ signifies that no data is provided. The labels (5) and (n) in column headers correspond to results estimated using five and $n$ point pairs, respectively. The multiple solutions of the essential matrix herein are identified using the true essential matrix.}
\label{tab:E_mat_problem}
\begin{tabular}{|c|c|c|c|c|c|}
\hline
Scenes & Noise Pixel & 5pt (5)~\cite{stewenius2006recent} & 5pt (n)~\cite{stewenius2006recent} & LiRP (n) & Npt (n)~\cite{zhao2020efficient} \\
\hline
\multirow{2}{*}{Planar} & 0   & $(0^\circ, 4.19e{-01})$ & $(1.60^\circ, 1.08e{-15})$ & $(0^\circ, 1.44)$ & $(1.99^\circ, -)$ \\
\cline{2-6}
                        & 0.1 & $(0.19^\circ, 4.45e{-01})$ & $(0.016^\circ, 1.40e{-04})$ & $(0.015^\circ, 1.28)$ & $(2.51^\circ, -)$ \\
\hline
\multirow{2}{*}{Normal} & 0   & $(0^\circ, 2.27e{-01})$ & $(0^\circ, 7.80e{-03})$ & $(0^\circ, 1.23)$ & $(0^\circ, -)$ \\
\cline{2-6}
                        & 0.1 & $(0.15^\circ, 3.10e{-01})$ & $(0.031^\circ, 7.56e{-03})$ & $(0.012^\circ, 1.32)$ & $(0.012^\circ, -)$ \\
\hline
\end{tabular}
\end{table*}

\subsection{Synthetic Experiment} 
\noindent 
\noindent The synthetic scenarios include two 3D scenes (normal vs. planar) and several types of motion (see Fig.~\ref{fig:RTX}). The normal scene was generated using OpenGV's code with its default parameters ~\cite{kneip2014opengv} to construct 3D feature points and camera poses for two views, retaining only bearing vectors that satisfy chirality constraints. The planar scene was intentionally generated to examine the algorithm's performance, in which 3D points were generated with a minimal depth (double the translation length) between the plane and the first position of the camera. We simulated several different motions to test the performance of different relative pose estimation methods, including forward motions, sideways motions along X/Y axes, and 3 degrees of freedom (DoF) motions.

The synthetic experiment was conducted in the Ubuntu 18.04 environment running on a single core. Monte Carlo tests were repeated 100 times. The maximum iteration in MS-TLS is fixed to be 10. The feature matching outliers were generated by a predetermined outlier fraction.  Before running the experiment, we categorized the compared algorithms into two groups. The first group consists of initial relative pose estimation methods (directly solving the relative pose), including Pizarro's 6pt algorithm~\cite{pizarro2003relative}, our proposed LiRP algorithm, homography algorithm (Homo) supported by OpenCV, and 5pt~\cite{stewenius2006recent} / 7pt~\cite{hartley2003multiple}  / 8pt~\cite{hartley2003multiple} algorithms supported by OpenGV. The second group comprises optimization techniques, including the $N$-point method~(Npt)~\cite{zhao2020efficient} , BA~\cite{hartley2003multiple}, the PPO optimization (PPOopt), and eigensolver~(EigSol)~\cite{kneip2013direct} / nonlinear optimization~(OpenGVopt)~\cite{kneip2014opengv} in OpenGV.


%，从理论上而言， 正确的本质矩阵在5点法和6点法的解空间(这个在理论讨论部分说）

\subsubsection{Planar Degeneracy in Current SOTA Algorithms for the N-point Problem} 


%是否需要强调是非minimal problem
\noindent Unlike the RANSAC method, IRLS (and its variety) is typically used to solve the N-point problem. The Stewenius method, a well-established approach for estimating the relative pose, is traditionally employed for sets of five matching points and can also be extended to handle more than five pairs of points ($n\geq6$)~\cite{stewenius2006recent}. It is widely recognized that both the 7pt and 8pt methods exhibit planar degeneracy. It has been pointed out~\cite{stewenius2006recent,pizarro2003relative} that the linear 6-point algorithm~\cite{philip1996non} also suffers from planar degeneracy, and specifically the Pizarro's 6pt method is prone to instability in solutions.

The noise-free experiment in Table.~\ref{tab:E_mat_problem} throws light on the theoretical limitation of the Stewenius method when addressing $n$-point pairs under planar configurations (3DoF motion). Specifically, in such a case, the action matrix constructed by using the Stewenius method~\cite{stewenius2006recent} for $n$ point pairs has repeated eigenvalues. The multiple eigenvectors corresponding to the repeated eigenvalues lead to infinite solutions of the essential matrix. We utilized an indicator $ d_{\min} = \min_{i,j} \{ |\lambda_i - \lambda_j| \}$ to quantify the minimum distance between the eigenvalues of the action matrix. Under a noise-free planar scene, the Stewenius method's $d_{\min}$ is zero, signifying the presence of duplicate eigenvalues. The rotation accuracy of $2.79e^{-2}$ indicates the above-mentioned limitation. Fortunately, when point pairs are subject to small noise like 0.1-pixel, this limitation is largely mitigated. The result in Table.~\ref{tab:E_mat_problem} also reveals the planar degenerate problem for the $N$-point method~\cite{zhao2020efficient}. In contrast, the LiRP algorithm consistently achieved the best accuracy in both normal and planar scenes, maintaining $d_{\min}$ larger than the acceptable thresholds. The LiRP algorithm's rotation accuracy further confirms its absence of planar case limitations.

% Considering the GNC-RANSAC sampling size $n_{sample}=30$,

\subsubsection{Noise tests} 

\textbf{Performance of solution identification schemes.} 
The OpenGV platform chooses the reprojection residual on the 2D spherical manifold $\mathcal{S}^2$ to identify the correct solution of the relative pose obtained from the 5pt algorithm, whereas the OpenCV platform selects  Sampson distance. Figure~\ref{fig:identification_methods} showcases the performance of the LiRP algorithm, against the 5pt algorithm, under three identification schemes in Sec.~\ref{sec:idm}. Overall, the PPO identification scheme (denoted by circles in different colors) lies at the bottom in all sub-figures, indicating its best accuracy among the three schemes for both LiRP and 5pt algorithms. 


Specifically, in 3DoF motion scenarios (in green legend), the OpenGV scheme (in green '+') exhibits comparable accuracy to PPO scheme (in green 'o') for normal and planar scenes, whereas the Sampson scheme (in green '$\times$') fails to identify the right solution in planar scenarios. For forward and sideways motions, the OpenGV identification scheme experiences a significant decay in accuracy for large noises. For example, in Fig.\ref{fig:idm_LiRP}, the rotation error of the OpenGV scheme for LiRP is about 5 times larger than the PPO scheme when the noise arrives at 3 and 9 pixels. The OpenGV scheme performs even worse for the 5pt algorithm in Fig.\ref{fig:idm_5pts}. \\



\begin{figure}[!h]
    \centering
    % 第一行
    \includegraphics[width=1\columnwidth]{images/identification/legend.png}\\
    % 第二行
    \subfloat[5pt in normal scene]{\includegraphics[width=0.45\columnwidth]{images/identification/5pts.png}\label{fig:idm_5pts}}\hfill
    \subfloat[LiRP in normal scene]{\includegraphics[width=0.45\columnwidth]{images/identification/LiRP.png}\label{fig:idm_LiRP}}\\
    % 第三行
    \subfloat[5pt in planar scene]{\includegraphics[width=0.45\columnwidth]{images/identification/5pts_planar.png}\label{fig:idm_5pts_planar}}\hfill
    \subfloat[LiRP  in planar scene]{\includegraphics[width=0.45\columnwidth]{images/identification/LiRP_planar.png}\label{fig:idm_LiRP_planar}}
    \caption{Performance of the LiRP algorithm, against the 5pt algorithm, under three identification schemes. Our LiRP together with the PPO identification scheme consistently yields the best accuracy and demonstrates its robustness to planar and specific motion scenarios.}
    \label{fig:identification_methods}
\end{figure}
\\
\noindent \textbf{Noise test for initial two-view methods.} 
Subsequently, we conducted tests to evaluate the impact of noises on relative pose estimation methods for $n$ point pairs, see Fig.~\ref{fig:test_ini_methods} and~\ref{fig:noise_opt_test}. We compared their performance across a spectrum of noise levels for 20 point pairs (zero outlier), which were increased from 0.1 to 10.1 pixels in a step of 1 pixel. Notably, 7pt and 8pt algorithms cannot handle the planar scenario and their results are not provided in this case.


\begin{figure*}[!h]
    \centering
    % 第一行
    \includegraphics[height=0.4cm\textwidth]{images/noise_without_outliers/initial/legend_noise_test_ini.png}\\
    % 第二行
    \subfloat[Normal scene with forward motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png}\label{fig:normal_forward}}\hfill
    \subfloat[Normal scene with sideways motion along the X-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png}\label{fig:normal_sidewaysX}}\hfill
    \subfloat[Normal scene with sideways motion along the Y-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png}\label{fig:normal_sidewaysY}}\hfill
    \subfloat[Normal scene with 3DoF motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png}\label{fig:normal_motion}}\\
    % 第三行
    \subfloat[Planar scene with forward motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png}\label{fig:planar_forward}}\hfill
    \subfloat[Planar scene with sideways motion along the X-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png}\label{fig:planar_sidewaysX}}\hfill
    \subfloat[Planar scene with sideways motion along the Y-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png}\label{fig:planar_sidewaysY}}\hfill
    \subfloat[Planar scene with 3DoF motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png}\label{fig:planar_motion}}
    \caption{Noise test results of initial relative pose estimation methods: (a-d) normal scenarios, (e-h) planar scenarios. Our LiRP consistently achieves the best accuracy and highest reliability across different scenes and motions.}
    \label{fig:test_ini_methods}
\end{figure*}

In normal scenarios, as evidenced by Fig.~\ref{fig:test_ini_methods}, our proposed algorithm demonstrates an outstanding accuracy, particularly for large noise levels.

Specifically, the testing results can be summarized as below:
\begin{itemize}
 \item For 3DoF motion under normal and planar scenes (Fig.~\ref{fig:normal_motion}), the rotation accuracy of various methods is quite close. However, the advantage of LiRP in rotation accuracy estimation is still obvious. 

 \item For specific motions under normal scenarios (Fig.~\ref{fig:normal_forward}~$\sim$~\ref{fig:normal_sidewaysY}), the rotation accuracy of LiRP significantly outperforms other methods including the SOTA 5pt method. The initial estimation methods except LiRP are apparently affected by specific sideways motions, demonstrating significant decay in rotation accuracy when the noise level surpasses 1 pixel.

 \item For specific motions in planar scenarios (Fig.~\ref{fig:planar_forward}~$\sim$~\ref{fig:planar_sidewaysY}), the observations are similar with counterparts under normal scenarios. LiRP still achieves the best rotational accuracy and consistently shows reliability across different motions. Other initial estimation methods in OpenGV are also significantly affected by the specific sideways motions. Notably, LiRP outperforms the homography method and the 5pt method, both provided in OpenCV. 
\end{itemize}

%----------------------------------------------------------------------------------------------------
\begin{figure*}[!htpb]
    \centering
    % 第一行
    \includegraphics[height=0.5cm\textwidth]{images/noise_without_outliers/opt/legend_noise_opt.png}\\
    % 第二行
    \subfloat[Normal scene with forward motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png}\label{fig:opt_normal_forward}}\hfill
    \subfloat[Normal scene with sideways motion along the X-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png}\label{fig:opt_normal_sidewaysX}}\hfill
    \subfloat[Normal scene with sideways motion along the Y-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png}\label{fig:opt_normal_sidewaysY}}\hfill
    \subfloat[Normal scene with 3DoF motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png}\label{fig:opt_normal_motion}}\\
    % 第三行
    \subfloat[Planar scene with forward motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png}\label{fig:opt_planar_forward}}\hfill
    \subfloat[Planar scene with sideways motion along the X-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png}\label{fig:opt_planar_sidewaysX}}\hfill
    \subfloat[Planar scene with sideways motion along the Y-axis]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png}\label{fig:opt_planar_sidewaysY}}\hfill
    \subfloat[Planar scene with 3DoF motion]{\includegraphics[width=0.22\textwidth]{images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png}\label{fig:opt_planar_motion}}
    \caption{Noise test results of optimized relative pose estimation methods. (a-d) Normal scenarios with different motions. (e-h) Planar scenarios with different motions. Algorithms in parentheses indicate the initial method used.  The LiRP algorithm achieves an accuracy level comparable to the optimization methods. }
    \label{fig:noise_opt_test}
\end{figure*}


\noindent \textbf{Noise test for optimized two-view methods.} 
The optimization is always the final step in two-view estimation after removing all potential matching outliers and giving a proper initial value of the relative pose. Figure~\ref{fig:noise_opt_test} shows that the LiRP algorithm achieves an accuracy level comparable to the optimized methods, particularly excelling in specific motion scenarios where it outperforms BA optimization. The PPOopt method exhibits the overall best performance across various combinations of motion structures and 3D scenes. In low-noise conditions (below 2 pixels), EigSols maintains a stable accuracy, but its performance is unstable for large noises or specific motions. The Npt method performs comparably to OpenGVopt in normal scenarios with 3DoF motion. We also tested the Npt method in planar scenarios, but did not illustrate it in Fig.~\ref{fig:opt_planar_forward} to Fig.~\ref{fig:opt_planar_motion} due to its failure in all planar tests. This result aligns with the findings presented in Table~\ref{tab:E_mat_problem}. Specifically, Npt, similar to the 7pt and 8pt methods, is affected by planar degeneracy issues. As depicted in Fig.~\ref{fig:test_ini_methods} and Fig.~\ref{fig:noise_opt_test}, it is evident that current approaches for solving the N-point problem demonstrate varying degrees of outliers when encountering specific motion structures and planar scenes.
\begin{table}[!htbp]
    \centering
    \caption{Two-view residuals}
    \label{tab:residuals}
    \begin{tabular}{c c}
        \toprule
        Types & Residuals \\
        \midrule
        BA & 
        \(\left\|\frac{R {\boldsymbol{X}}_{i}^C + {\boldsymbol{t}}}{\boldsymbol{e}_3^T (R {\boldsymbol{X}}_{i}^C + {\boldsymbol{t}})}-{{\boldsymbol x}}_i^{\prime}\right\|\) \\
        \midrule
        
        OpenGV & 
        \(\sum_{i=1}^n \left(2-\tilde{\boldsymbol{x}}_i^T \tilde{\boldsymbol{X}}_{i}^C-\tilde{\boldsymbol{x}}_i^{\prime T} \tilde{\boldsymbol{Y}}_{i}^C\right),\) \(\boldsymbol{Y}_{i}^C=R {\boldsymbol{X}}_{i}^C + {\boldsymbol{t}}\) \\
        \midrule
        
        Coplanar & 
        \(\left\|\tilde{\boldsymbol{x}}_i^{\prime T} E \tilde{\boldsymbol{x}}_i\right\|\) \\
        \midrule
        
        LiRT & 
        \(\left\|\tilde L_i \boldsymbol{t}\right\|\) \\
        \midrule
        
        PPO & 
        \(\left\|\tilde{\boldsymbol{y}}_i - \tilde{\boldsymbol x}_i^{\prime}\right\|, \quad {\boldsymbol{y}}_i=\left\|\boldsymbol{t} \times \tilde{\boldsymbol x}_i^{\prime}\right\| R \tilde{\boldsymbol x}_i+\left\|\tilde{\boldsymbol x}_i^{\prime} \times R \tilde{\boldsymbol x}_i\right\| \boldsymbol{t}\) \\
        
        \bottomrule
    \end{tabular}
    \begin{tablenotes}
        \item[*] The 3D points $\boldsymbol X^C_i$ are triangulated by the relative pose, and $\boldsymbol{e}_3^T$ is the third row of the identity matrix.
    \end{tablenotes}
\end{table}


\subsubsection{Outlier tests} 
We have tested all of the above motion types but only reported the results for 3DoF motion due to the page limit. \\

\noindent  \textbf{Selection of Residual Statistics and Initial Relative Pose Estimation Methods.} 
\noindent In the sequel, we compared the five optional residual statistics in Table.\ref{tab:residuals} and then selected the optimal one to be combined with the MS-TLS scheme in Algorithm.~\ref{alg:IRLS-Pose-only}. Based on the optimal residual, we tested the performance of each initial method and selected the optimal one to be integrated with the GNC-RANSAC algorithm. 

Here, we incorporated the RANSAC-based 5pt method in OpenGV with 200 maximum iterations as a comparison benchmark. The number of point pairs in the MS-TLS scheme is $n = 20$, with image noise fixed at 1 pixel.

Specifically, Fig.~\ref{fig:outlier_v_test} displays the median error results of different residual statistics as a function of outlier fractions. We have tested $\boldsymbol v^{LiRT}$ residual but only illustrated the $\boldsymbol v^{PPO}$ results due to their comparable accuracy. It is obvious that the MS-TLS scheme utilizing $\boldsymbol v^{PPO}$ as the re-weighted iterative residual statistic yields the highest rotation accuracy, which ensures the rotation error close to the benchmark result (dashed line) for up to 50\% outlier fractions in both normal and planar scenes. The residuals $\boldsymbol v^{Coplanar}$ and $\boldsymbol v^{BA}$ can only well deal with about 20\% outlier fraction, while the residual $\boldsymbol v^{OpenGV}$ well resists around 30\% percentage of outliers. 

%

\begin{figure}
    \centering
    % Adjust the width as needed, for example to 75% of the text width
    \subfloat[Normal scene with 3DoF motion]{%
        \includegraphics[width=0.5\linewidth]{images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png}
        \label{fig:outlier_v_test_a}}
    \subfloat[Planar scene with 3DoF motion]{%
        \includegraphics[width=0.5\linewidth]{images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png}
        \label{fig:outlier_v_test_b}}
    \caption{Robustness test for different residuals. All variants of MS-TLS utilize LiRP for relative rotation estimation.  The legend 'Benchmark' represents the RANSAC-based 5pt method in OpenGV with maximum 200 iterations. The MS-TLS scheme with PPO residual can well resist up to 50\% outlier fractions in both normal and planar scenes.}
    \label{fig:outlier_v_test}
\end{figure}

Figure~\ref{fig:mstls_initial_test} illustrates the outlier handling performance of different initial relative pose estimation methods when combined with the MS-TLS method. The results indicate that the MS-TLS outlier estimation scheme using the LiRP method is consistently near to the RANSAC+5pt method, which can well resist 50\% outliers. In contrast, the performance of other methods combined with MS-TLS is inferior to LiRP. For example, the current SOTA 5pt method combined with MS-TLS fails to accurately estimate the relative rotation when the outlier fraction surpasses 30\% in both normal and planar scenes with 3 DoF motion. This failure occurs even when the identification method is effective, as illustrated in Fig.~\ref{fig:identification_methods}.\\



\begin{figure}
    \centering
    % Adjust the width as needed, for example to 75% of the text width
    \subfloat[Normal scene with 3DoF motion]{%
        \includegraphics[width=0.5\linewidth]{images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png}
        \label{fig:mstls_initial_normal}}
    \subfloat[Planar scene with 3DoF motion]{%
        \includegraphics[width=0.5\linewidth]{images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png}
        \label{fig:mstls_initial_planar}}
    \caption{Performance of initial methods on MS-TLS scheme. The residual statistic is fixed to $\boldsymbol v^{PPO}$.}
    \label{fig:mstls_initial_test}
\end{figure}


\noindent \textbf{Results of Our GNC-RANSAC Scheme.}
For reasonable rotation estimation with the outlier fraction over 50\%, we proposed a fusion strategy by combining GNC and RANSAC. We set the image noise to $1$ pixel, chose $n_{s} = 20$, and conducted tests in both normal and planar scenarios for $n = 300$. 


The simulation results of the proposed scheme at maximum 50 iterations are shown in  Fig.~\ref{fig:gnc-ransac-test}, as compared with the SOTA methods under maximum 2000 (default in OpenMVG platform) iterations, including MAGSAC++(\cite{barath2020magsac++}), RANSAC with Stewenius's 5pt~\cite{stewenius2006recent}) in OpenGV, and LMedS/RHO with Nister's 5pt~\cite{nister2004efficient}) in OpenCV. In normal scenes, the compared methods resist up to 50 $\sim$ 60\% outlier fractions; in planar scenes, OpenGV resists up to 50 $\sim$ 60\%, but MAGSAC++ and the OpenGV methods totally collapse due to solution identification failure. For outlier fractions from 0\% to 80\%, our strategy consistently ensured high-accuracy rotation results. Remarkably, at outlier fractions up to 70\%, it significantly outperforms other strategies in both normal and planar scenarios, with an accuracy improvement of nearly two orders of magnitude. Meanwhile, at lower outlier fractions, the strategy maintains a considerable lead in rotation accuracy. Note that the rotation accuracy of robust Npt is inconsistent with that reported in~\cite{zhao2020efficient} because of a simulation issue of chirality therein. Please see the supplementary for details.

\begin{figure}
    % Adjust the width as needed, for example to 75% of the text width
    \includegraphics[width=0.35\textwidth]{images/gnc-ransac-figures/legend.png}
    \centering
    \subfloat[Normal scene with 3DoF motion]{%
        \centering
        \includegraphics[width=1\columnwidth]{images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png}
        \label{fig:gnc_ransac_normal_mean}}
        
    \subfloat[Planar scene with 3DoF motion]{%
        \centering
        \includegraphics[width=1\columnwidth]{images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png}
        \label{fig:gnc_ransac_planar_mean}}
    \caption{Rotation accuracy of our scheme as compared with state-of-art methods. Numbers in parentheses indicate the maximum iterations used. Our strategy consistently ensured high-accuracy rotation results for up to 70\% outlier fraction.}
    \label{fig:gnc-ransac-test}
\end{figure}

\subsection{Real Data Experiments}
The methods of OpenGV (RANSAC with Stewenius's 5pt~\cite{stewenius2006recent}), OpenCV (USAC\_MAGSAC++ with Nister's 5pt~\cite{nister2004efficient}), and MAGSAC++~\cite{barath2020magsac++} are similarly tested under maximum 50 (default in OpenGV) and 2000 (default in OpenMVG) iterations. The compared results are all initial relative pose estimation under the same default parameters.

\subsubsection{Strecha Results}
\noindent For each Strecha data, we obtained raw pair matches between two views by using SIFT~\cite{lowe2004distinctive} and the Cascade hashing~\cite{cheng2014fast} for feature extraction and matching, respectively. Then, we only proceeded with those views that have over 30 point pairs.



\begin{figure}
    \centering
    \includegraphics[width=1\columnwidth]{images/Strecha/strecha_Rerr.png}
    \caption{Relative rotation errors (mean and median) on Strecha dataset. The mean/median accuracy of our scheme is consistently superior to those of OpenGV and OpenCV.}
    \label{fig:StrechaErr}
\end{figure}


\begin{figure}
    \centering
    \subfloat[]{%
        \centering
        \includegraphics[width=0.4\linewidth]{images/Strecha/strecha_repeated.png}
        \label{fig:repeated}}%
    \subfloat[]{%
        \centering
        \includegraphics[width=0.55\linewidth]{images/Strecha/strecha_reconstruction.png}
        \label{fig:poses_3DHerz}}
    \caption{Mismatches and reconstruction results on the Strecha dataset
 (a) Repeated textures and mismatches in Castle-P30; 
(b) 3D reconstruction of Herz-Jesus-P25. The high-quality 3D reconstruction implies excellent rotation/translation accuracy.}
    \label{fig:Strecha1}
\end{figure}



In this experiment, we compared our scheme at only maximum 50 iterations with the current SOTA methods in OpenGV and OpenCV. From the relative rotation error results in Fig.~\ref{fig:StrechaErr}, it is evident that for various data in the Strecha dataset, the mean/median accuracy of our scheme is consistently superior to those of OpenGV and OpenCV. As shown in Fig.~\ref{fig:StrechaErr}, the GNC-RANSAC method is generally superior to OpenGV by 2 $\sim$ 10 times across the Strecha dataset. Particularly, it exhibits a median error of 0.06 for Herz-Jesus-P8, which is notably lower than OpenGV's 0.36. 

Regarding the Castle-P19 and Castle-P30 datasets specifically, there exist repetitive textures causing a large number of mismatches (as shown in Fig.~\ref{fig:Strecha1}) and resulting in a significant increase of relative rotation errors for most methods. The proposed GNC-RANSAC method outperforms other methods in handling those mismatches (see also Fig.~\ref{fig:gnc-ransac-test}).

Given the relative rotations, Fig.~\ref{fig:poses_3DHerz} exemplifies the global poses and 3D points of Herz-Jesus-P25, which was reconstructed by the pipeline of Chatterjee's global rotation averaging method~\cite{chatterjee2017robust},  global translation by LiGT, and then analytical reconstruction~\cite{cai2021pose}. The high-quality 3D reconstruction, even in the absence of global optimization, implies the excellent rotation/translation accuracy.


\begin{table*}[htbp]
    \centering
    \caption{Mean (\(v_{\text{mean}}\)) and Median (\(v_{\text{med}}\)) Errors of Different Methods on the RANSAC2020 Dataset}
    \label{tab:ransac2020Test}
    \resizebox{\textwidth}{!}{%
    \begin{tabular}{lcccccccccc}
        \toprule
        \multirow{2}{*}{Method} & \multicolumn{5}{c}{st peters square} & \multicolumn{5}{c}{sacre coeur} \\
        \cmidrule(lr){2-6} \cmidrule(lr){7-11}
        & \(v^{\text{BA}}_{\text{mean}}\) & \(v^{\text{Coplanar}}_{\text{mean}}\) & \(v^{\text{OpenGV}}_{\text{mean}}\) & \(v^{\text{PPO}}_{\text{mean}}\) & \(v^{\text{LiRT}}_{\text{mean}}\) & \(v^{\text{BA}}_{\text{mean}}\) & \(v^{\text{Coplanar}}_{\text{mean}}\) & \(v^{\text{OpenGV}}_{\text{mean}}\) & \(v^{\text{PPO}}_{\text{mean}}\) & \(v^{\text{LiRT}}_{\text{mean}}\) \\
        \midrule
        OpenGV(50) & 4.59E-04 & 2.36E-04 & 1.66E-07 & 6.01E-04 & 1.21E-04 & 4.58E-04 & 1.98E-04 & 1.68E-07 & 5.64E-04 & 1.01E-04 \\
        MAGSAC++(50) & 6.15E-04 & 2.08E-04 & 8.98E-02 & 3.84E-02 & 6.92E-05 & 4.96E-04 & 1.92E-04 & 6.95E-02 & 3.12E-02 & 6.29E-05 \\
        OpenCV(50) & 8.73E-04 & 4.24E-04& 1.51E-01 & 6.37E-02 & 2.01E-04 & 1.06E-03 & 4.18E-04 & 1.52E-01 & 7.35E-02 & 2.46E-04 \\
        Ours(50) & \textcolor{red}{\textbf{3.17E-04}} & \textcolor{red}{\textbf{1.84E-04}}& \textbf{1.41E-04}& \textcolor{red}{\textbf{3.78E-04}} & \textcolor{red}{\textbf{5.09E-05}}& \textbf{3.74E-04}& \textbf{1.62E-04}& \textbf{3.76E-03}& \textcolor{red}{\textbf{3.86E-04} }& \textcolor{red}{\textbf{3.59E-05}} \\
        OpenGV(2000) & 4.44E-04 & 2.12E-04 & \textcolor{red}{\textbf{1.49E-07}}& \textbf{5.83E-04} & 8.34E-05 & 4.27E-04 & 1.64E-04 & \textcolor{red}{\textbf{1.43E-07}}& \textbf{5.18E-04} & 6.85E-05 \\
        MAGSAC++(2000) & \textbf{4.01E-04}& \textbf{1.96E-04}& 6.12E-02 & 2.34E-02 & \textbf{6.35E-05}& \textcolor{red}{\textbf{3.73E-04}} & \textcolor{red}{\textbf{1.55E-04}} & 5.86E-02 & 2.60E-02 & \textbf{5.00E-05}\\
        OpenCV(2000) & 6.54E-04 & 4.21E-04 & 9.32E-02 & 3.44E-02 & 1.56E-04 & 1.06E-03 & 2.56E-04 & 9.81E-02 & 3.95E-02 & 9.69E-05 \\
        \midrule
        & \(v^{\text{BA}}_{\text{med}}\) & \(v^{\text{Coplanar}}_{\text{med}}\) & \(v^{\text{OpenGV}}_{\text{med}}\) & \(v^{\text{PPO}}_{\text{med}}\) & \(v^{\text{LiRT}}_{\text{med}}\) & \(v^{\text{BA}}_{\text{med}}\) & \(v^{\text{Coplanar}}_{\text{med}}\) & \(v^{\text{OpenGV}}_{\text{med}}\) & \(v^{\text{PPO}}_{\text{med}}\) & \(v^{\text{LiRT}}_{\text{med}}\) \\
        \midrule
        OpenGV(50) & 3.98E-04 & 1.71E-04 & 7.34E-08 & 4.91E-04 & 5.12E-05 & 4.03E-04 & 1.26E-04 & 7.63E-08 & 4.35E-04 & 3.80E-05 \\
        MAGSAC++(50) & 3.20E-04 & 1.27E-04 & 5.19E-08 & 4.19E-04 & \textbf{1.86E-05} & 3.32E-04 & 1.03E-04 & 5.70E-08 & 3.90E-04 & 1.68E-05 \\
        OpenCV(50) & 3.83E-04 & 1.80E-04 & 7.75E-08 & 5.06E-04 & 3.13E-05 & 3.95E-04 & 1.46E-04 & 8.65E-08 & 4.75E-04 & 2.54E-05 \\
        Ours(50) & \textcolor{red}{\textbf{1.84E-04}} & \textcolor{red}{\textbf{7.30E-05}} & \textcolor{red}{\textbf{1.54E-08}} & \textcolor{red}{\textbf{2.27E-04}} & \textcolor{red}{\textbf{1.01E-05}} & \textcolor{red}{\textbf{2.11E-04}} & \textcolor{red}{\textbf{6.43E-05}} & \textcolor{red}{\textbf{2.11E-08}} & \textcolor{red}{\textbf{2.29E-04}} & \textcolor{red}{\textbf{8.14E-06}} \\
        OpenGV(2000) & 3.68E-04 & 1.46E-04 & 6.22E-08 & 4.64E-04 & 3.76E-05 & 3.51E-04 & 9.68E-05 & 5.72E-08 & 4.03E-04 & 1.92E-05 \\
        MAGSAC++(2000) & \textbf{2.42E-04}& \textbf{1.22E-04}& \textbf{2.90E-08} & \textbf{3.14E-04} & 2.45E-05& \textbf{2.52E-04}& \textbf{7.54E-05}& \textbf{3.12E-08}& \textbf{2.79E-04}& \textbf{1.30E-05}\\
        OpenCV(2000) & 3.86E-04 & 1.99E-04 & 7.36E-08 & 4.88E-04 & 4.22E-05 & 3.78E-04 & 1.20E-04 & 7.24E-08 & 4.28E-04 & 2.05E-05 \\
        \bottomrule
    \end{tabular}
    }
    \begin{tablenotes}
        \item[*] The bold red indicates the minimum and bold black indicates the second minimum values.
    \end{tablenotes}
\end{table*}



\begin{figure}
    \centering
    % Adjust the width as needed, for example to 75% of the text width
    \subfloat[st peters square]{%
        \includegraphics[width=1\columnwidth]{images/ransac2020/st_peters_square_R_error_box4.png}\label{fig:st_peters_square_R_error_box}}\\
        
    \subfloat[sacre coeur]{%
        \includegraphics[width=1\columnwidth]{images/ransac2020/sacre_coeur_R_error_box4.png}\label{fig:sacre_coeur_R_error_box}}
    \caption{Rotation accuracy of different schemes. The values in parenthese denote maximum iterations. }
    \label{fig:Rerror_ransac2020}
\end{figure}

\subsubsection{RANSAC2020 Results}
We further conducted experiments on the RANSAC2020 dataset, such as the 'st peters square' and 'sacre coeur' data. All matches with scores below 0.9 were used for the two-view estimation. As the RANSAC2020 dataset does not provide the ground truth, we assessed the estimation results of the first 1000 two-views, not only against COLMAP but also considering other error metrics. 


Figure~\ref{fig:Rerror_ransac2020} compares the performance of our proposed method (maximum iterations of 50) with the SOTA schemes provided in OpenGV, OpenCV, and MAGSAC++ (maximum iterations of 50 and 2000). 

At the maximum iteration of 50, our method demonstrates rotational accuracy (in terms of median errors) approximately 40 times better than OpenGV and about 6 times better than OpenCV and MAGSAC++. When the iteration limit is increased to 2000, the SOTA schemes are significantly enhanced and become comparable to our methods. Specifically, the MAGSAC++ achieves the best, closely followed by our method. This highlights the efficiency of our approach in achieving high accuracy with significantly fewer iterations.


%It is important to note that the accuracy of the RANSAC+5pt scheme in OpenGV in the RANSAC2020 dataset is not as prominent as observed in simulations. This discrepancy arises because the default parameters for OpenGV's inlier threshold are probably not well-suited for this dataset. In this context, MAGSAC++ demonstrates its superiority over the traditional RANSAC approach thanks to the advantage of the MAGSAC++ framework.

As shown in Table.~\ref{tab:ransac2020Test}, our method demonstrates the best performance across various residuals, followed by MAGSAC++ at maximum 2000 iterations. In terms of median errors, our method at maximum 50 iterations performs consistently the best and MAGSAC++ at maximum 2000 iterations follows as the runner-up. The reason that the ranking conclusion here is a bit inconsistent with that in Fig.~\ref{fig:Rerror_ransac2020} might be owed to the unavailable ground truth of RANSAC2020.\\


In summary, the experimental results on the Strecha/RANSAC2020 dataset highlight the efficacy of our proposed method in handling various scenarios. The excellent performance across multiple metrics indicates that our method not only improves accuracy but also enhances robustness against outliers and noise.


\section{Conclusions}
\label{sec:conclusion}
\noindent This paper introduces a novel linear relative pose estimation algorithm to handle planar degenerate scenes by solving the N-point problem. The proposed algorithm leverages the pose-only imaging geometry to construct residuals for identifying image-matching outliers and integrates GNC-IRLS and RANSAC for improved robustness and accuracy.

Firstly, we presented the general solution form of the essential equation under planar and pure rotational cases, offering a linear estimation method for the weighted essential matrix in the N-point problem. Secondly, we introduced pose-only constraints into the relative pose estimation pipeline, enhancing outlier handling and right solution identification under various motion structures. Thirdly, we combined the proposed scheme with GNC-IRLS / RANSAC, significantly improving robustness and accuracy even in very high outlier fractions.

Experimental results on both synthetic and real-world datasets demonstrate that our method achieves 2 to 10 times improvement in relative rotation accuracy even with up to 70\% outliers, as compared to the state-of-the-art methods. Specifically, the proposed algorithm consistently maintains high accuracy and robustness across various scenarios such as normal and planar scenes.

Future work can be focused on refining the IRLS and RANSAC components, exploring adaptive parameter tuning strategies, and investigating alternative robust estimation techniques to even further enhance the algorithm's performance and applicability.

% An example of a floating figure using the graphicx package.
% Note that \label must occur AFTER (or within) \caption.
% For figures, \caption should occur after the \includegraphics.
% Note that IEEEtran v1.7 and later has special internal code that
% is designed to preserve the operation of \label within \caption
% even when the captionsoff option is in effect. However, because
% of issues like this, it may be the safest practice to put all your
% \label just after \caption rather than within \caption{}.
%
% Reminder: the "draftcls" or "draftclsnofoot", not "draft", class
% option should be used if it is desired that the figures are to be
% displayed while in draft mode.
%
%\begin{figure}[!t]
%\centering
%\includegraphics[width=2.5in]{myfigure}
% where an .eps filename suffix will be assumed under latex, 
% and a .pdf suffix will be assumed for pdflatex; or what has been declared
% via \DeclareGraphicsExtensions.
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure}

% Note that the IEEE typically puts floats only at the top, even when this
% results in a large percentage of a column being occupied by floats.
% However, the Computer Society has been known to put floats at the bottom.


% An example of a double column floating figure using two subfigures.
% (The subfig.sty package must be loaded for this to work.)
% The subfigure \label commands are set within each subfloat command,
% and the \label for the overall figure must come after \caption.
% \hfil is used as a separator to get equal spacing.
% Watch out that the combined width of all the subfigures on a 
% line do not exceed the text width or a line break will occur.
%
%\begin{figure*}[!t]
%\centering
%\subfloat[Case I]{\includegraphics[width=2.5in]{box}%
%\label{fig_first_case}}
%\hfil
%\subfloat[Case II]{\includegraphics[width=2.5in]{box}%
%\label{fig_second_case}}
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure*}
%
% Note that often IEEE papers with subfigures do not employ subfigure
% captions (using the optional argument to \subfloat[]), but instead will
% reference/describe all of them (a), (b), etc., within the main caption.
% Be aware that for subfig.sty to generate the (a), (b), etc., subfigure
% labels, the optional argument to \subfloat must be present. If a
% subcaption is not desired, just leave its contents blank,
% e.g., \subfloat[].


% An example of a floating table. Note that, for IEEE style tables, the
% \caption command should come BEFORE the table and, given that table
% captions serve much like titles, are usually capitalized except for words
% such as a, an, and, as, at, but, by, for, in, nor, of, on, or, the, to
% and up, which are usually not capitalized unless they are the first or
% last word of the caption. Table text will default to \footnotesize as
% the IEEE normally uses this smaller font for tables.
% The \label must come after \caption as always.
%
%\begin{table}[!t]
%% increase table row spacing, adjust to taste
%\renewcommand{\arraystretch}{1.3}
% if using array.sty, it might be a good idea to tweak the value of
% \extrarowheight as needed to properly center the text within the cells
%\caption{An Example of a Table}
%\label{table_example}
%\centering
%% Some packages, such as MDW tools, offer better commands for making tables
%% than the plain LaTeX2e tabular which is used here.
%\begin{tabular}{|c||c|}
%\hline
%One & Two\\
%\hline
%Three & Four\\
%\hline
%\end{tabular}
%\end{table}


% Note that the IEEE does not put floats in the very first column
% - or typically anywhere on the first page for that matter. Also,
% in-text middle ("here") positioning is typically not used, but it
% is allowed and encouraged for Computer Society conferences (but
% not Computer Society journals). Most IEEE journals/conferences use
% top floats exclusively. 
% Note that, LaTeX2e, unlike IEEE journals/conferences, places
% footnotes above bottom floats. This can be corrected via the
% \fnbelowfloat command of the stfloats package.





% if have a single appendix:
%\appendix[Proof of the Zonklar Equations]
% or
%\appendix  % for no appendix heading
% do not use \section anymore after \appendix, only \section*
% is possibly needed

% use appendices with more than one appendix
% then use \section to start each appendix
% you must declare a \section before using any
% \subsection or using \label (\appendices by itself
% starts a section numbered zero.)
%



% use section* for acknowledgment
\ifCLASSOPTIONcompsoc
  % The Computer Society usually uses the plural form
  \section*{Acknowledgments}
\else
  % regular IEEE prefers the singular form
  \section*{Acknowledgment}
\fi

This work was supported in part by National Key R\&D Program (2022YFB3903802), National Natural Science Foundation of China (62273228), and Shanghai Jiao Tong University Scientific and Technological Innovation Funds.


% Can use something like this to put references on a page
% by themselves when using endfloat and the captionsoff option.
\ifCLASSOPTIONcaptionsoff
  \newpage
\fi



% trigger a \newpage just before the given reference
% number - used to balance the columns on the last page
% adjust value as needed - may need to be readjusted if
% the document is modified later
%\IEEEtriggeratref{8}
% The "triggered" command can be changed if desired:
%\IEEEtriggercmd{\enlargethispage{-5in}}

% references section

% can use a bibliography generated by BibTeX as a .bbl file
% BibTeX documentation can be easily obtained at:
% http://mirror.ctan.org/biblio/bibtex/contrib/doc/
% The IEEEtran BibTeX style support page is at:
% http://www.michaelshell.org/tex/ieeetran/bibtex/
\bibliographystyle{IEEEtran} % here load IEEEtran.bst
\bibliography{main}
%
% <OR> manually copy in the resultant .bbl file
% set second argument of \begin to the number of references
% (used to reserve space for the reference number labels box)

% \begin{thebibliography}{1}

% \bibitem{IEEEhowto:kopka}
% H.~Kopka and P.~W. Daly, \emph{A Guide to {\LaTeX}}, 3rd~ed.\hskip 1em plus
%   0.5em minus 0.4em\relax Harlow, England: Addison-Wesley, 1999.

% \end{thebibliography}

% biography section
% 
% If you have an EPS/PDF photo (graphicx package needed) extra braces are
% needed around the contents of the optional argument to biography to prevent
% the LaTeX parser from getting confused when it sees the complicated
% \includegraphics command within an optional argument. (You could create
% your own custom macro containing the \includegraphics command to make things
% simpler here.)
%\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{mshell}}]{Michael Shell}
% or if you just want to reserve a space for a photo:


% if you want to include a photo:
\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{images/cq.png}}]{Qi Cai}
received the B.Sc. and M.S. degrees respectively in School of Geosciences and Info-Physics and School of Aeronautics and Astronautics, Central South University, in 2015 and 2018. He is now pursuing his Ph.D. degree in School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University. His research interests include geodesy, computer vision and inertial-visual fusion for navigation.
\end{IEEEbiography}


\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{images/xinruiLi.jpeg}}]{Xinrui Li}
received the B.Sc. degree in School of Physics, University of Electronic Science and Technology of China. He is now pursuing his Ph.D. degree in School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University. He has a strong interest in computer vision geometry and Simultaneous Localization and Mapping.
\end{IEEEbiography}


\begin{IEEEbiography}
[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{images/yuanxinWu.jpg}}]{Yuanxin Wu}
received the B.Sc. and Ph.D. degree in navigation from National University of Defense Technology. He was with National University of Defense Technology as a Lecturer and an Associate Professor, University of Calgary as a visiting Scholar, and Central South University as a Professor. He is currently a Professor in School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, China. His current research interests include inertial-based navigation system, inertial-visual fusion and wearable human motion sensing.
He was the recipient of NSFC Award for Excellent Young Scientists, Natural Science and Technology Award in University, and Elsevier’s Highly Cited Chinese Researchers. He serves as an Associate Editor for IEEE Trans. on Aerospace and Electronic Systems, Program Committee for DGON Inertial Sensors and Systems and IEEE AESS Distinguished Lecturer.
\end{IEEEbiography}


\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{images/wenxianYu.png}}]{Wenxian Yu}
received the B.Sc., M.Sc., and Ph.D. degrees from the National University of Defense Technology, Changsha, China, in 1985, 1988, and 1993, respectively. From 1996 to 2008, he was a Professor with the College of Electronic Science and Engineering, National University of Defense Technology, where he served as the deputy dean of the school and assistant director of the National Key Laboratory of Automatic Target Recognition. In 2008, He joined the School of Electronics, Information, and Electrical Engineering, Shanghai Jiao Tong University, Shanghai. He served as the executive dean of the School from 2009 to 2011. Currently, he is a Chaired Professor in the University.
\end{IEEEbiography}

% insert where needed to balance the two columns on the last page with
% biographies
%\newpage


% You can push biographies down or up by placing
% a \vfill before or after them. The appropriate
% use of \vfill depends on what kind of text is
% on the last page and whether or not the columns
% are being equalized.

%\vfill

% Can be used to pull up biographies so that the bottom of the last one
% is flush with the other column.
%\enlargethispage{-5in}



% that's all folks
\end{document}


