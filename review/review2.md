

# Reply to AE's Comments

We thank the Associate Editor (AE) for summarizing the reviewers' concerns and providing an opportunity to address them. Below, we respond to each point systematically, drawing on our replies to the reviewers and incorporating additional evidence to clarify our contributions.

We respectfully note significant differences in how our contributions were evaluated by the two reviewers. While we appreciate all feedback, we believe there may be some fundamental misunderstandings regarding our core theoretical and experimental contributions in Reviewer 1's comments. Specifically, our work presents two key innovations that appear to have been overlooked:

1. We present a general solution form to the essential equation considering planar scenes and pure rotational motions, and establish its relationship to the homography matrix in planar cases. In particular, the homography matrix can be linearly represented by the three smallest singular vectors of the general solution to the essential equation.

2. The essential matrix is linearly obtained for the first time by solving the N-point problem. We introduce the pose-only imaging geometrical constraints into the relative pose estimation pipeline to effectively screen out feature matching outliers and identify the correct relative pose (without 3D reconstruction).

We observe that Reviewer 1's evaluation appears to focus on certain aspects while not fully acknowledging these novel theoretical contributions and their experimental validation. Additionally, some claims regarding state-of-the-art methods (e.g., PoseLib) may benefit from further clarification based on recent literature. In contrast, Reviewer 2's constructive feedback affirmed our innovation and provided valuable suggestions for additional experiments, which we will thoroughly address in the revision.

We believe these concerns can be resolved through clarification and additional experimental validation, and we respectfully request the opportunity to revise and resubmit to TPAMI.

**1. Missing discussion and comparison with related work (DEGENSAC and Q-DEGSAC).**

We acknowledge that our initial submission could have included more explicit discussion of DEGENSAC [Chum et al., CVPR 2005] and Q-DEGSAC [Frahm and Pollefeys, CVPR 2006], which handle planar degeneracy via model switching. However, as noted in our reply to Reviewers, these methods are not the latest SOTA for calibrated relative pose; ETH Zurich's MAGSAC++ [Barath et al., 2020] is declared SOTA on its GitHub ([https://github.com/danini/magsac](https://github.com/danini/magsac)), outperforming DEGENSAC in high-outlier scenarios without switching, as confirmed by benchmarks and papers therein. In the revision, we will add detailed discussions and direct comparisons with DEGENSAC/Q-DEGSAC on planar subsets of Strecha and Phototourism datasets, demonstrating LiRP's better accuracy and efficiency.

**2. Concerns about the fairness of the experimental comparisons.**

As addressed in our reply to Reviewer 2, iteration caps were chosen based on theoretical minimums for GNC-RANSAC (e.g., 50 iterations suffice for 99% confidence per Table 2), contrasting with defaults like 2000 in OpenMVG for minimal solvers. We agree differing costs warrant unification; the revision will standardize to 100,000 iterations and include runtime-vs-accuracy plots (ms vs. AUC@5/10/20°). This ensures fairness while highlighting GNC-RANSAC's efficiency for n-point solvers.

**3. Missing comparisons with relevant baselines and state-of-the-art methods.**

We respectfully disagree with this assessment. Our baseline selection is methodologically sound and comprehensive:

**Core algorithm analysis reveals PoseLib is not SOTA.** Table S1 demonstrates that PoseLib relies on the outdated 2004 Nistér 5-point solver, while more advanced libraries use the 2006 Stewenius variant. ETH's Kneip (OpenGV author) confirms this limitation. Table S2 further shows that existing methods suffer from degeneracy issues that our LiRP method uniquely resolves through linear solutions.

**Table S1: Library Core Algorithms**
| Library | Core Algorithm                   | Year (Library Release)        | Core Algorithm Year           |
| ------- | -------------------------------- | ----------------------------- | ----------------------------- |
| PoseLib | Nistér 5pt (relpose_5pt)         | 2020 (GitHub)                 | 2004 (Nistér)                 |
| OpenGV  | Stewenius 5pt (fivept_stewenius) | 2014 (Kneip)                  | 2006 (Stewenius)              |
| ColMAP  | PoseLib 5pt (relpose_5pt)        | 2016 (ColMAP)                 | 2004 (Nistér)                 |
| OpenMVG | Stewenius 5pt                    | 2013 (OpenMVG)                | 2006 (Stewenius)              |
| Theia   | Stewenius 5pt                    | 2015                          | 2006 (Stewenius)              |
| OpenCV  | Nistér 5pt / MAGSAC++            | 2011 (USAC) / 2020 (MAGSAC++) | 2004 (Nistér) / 2020 (Barath) |

**Table S2: Algorithm Degeneracy Handling Comparison**
| Algorithm            | Year | Minimal Solver | N-point Solver   | Degenerate Handling                                              |
| -------------------- | ---- | -------------- | ---------------- | ---------------------------------------------------------------- |
| Philip 5/6pt         | 1996 | 5pt/6pt        | -                | Planar unstable, multi-solutions                                 |
| Pizarro 6pt          | 2003 | 6pt            | -                | Planar handling unstable                                         |
| Nistér 5pt           | 2004 | 5pt            | -                | Planar adds dimensions, high instability                         |
| Stewenius-Nistér 5pt | 2006 | 5pt (Gröbner)  | Extendable       | N-point faces repeated eigenvalues in planar, infinite solutions |
| Kneip 5pt            | 2012 | 5pt            | -                | Depth loss, multi-solutions                                      |
| Zhao N-point         | 2020 | 8pt            | Yes (convex opt) | Planar fails                                                     |
| LiRP (Ours)          | 2025 | 6pt            | Yes              | None (linear solve, no degeneracy issues)                        |

**Direct experimental validation confirms our superiority.** Figure 1 shows comprehensive noise testing comparing OpenGV, PoseLib, and our LiRP methods. Results demonstrate that PoseLib's 8-point solver exhibits significantly higher rotation errors (>0.2° at noise level 5) compared to our LiRP variants (<0.09°), validating our theoretical analysis.

**MAGSAC++ represents the true SOTA for robust estimation.** We compared against MAGSAC++ rather than DEGENSAC because MAGSAC++ has been established as the current SOTA by ETH's vision group, integrated into OpenCV, and proven superior to DEGENSAC through comprehensive benchmarks. Our experiments demonstrate consistent superiority over MAGSAC++ across multiple scenarios (Fig. 13, Table 3), establishing transitivity: LiRP > MAGSAC++ > DEGENSAC.

**Comprehensive baseline coverage.** We will add PoseLib, DEGENSAC, and Q-DEGSAC comparisons in the revision for completeness, though our current results already demonstrate superiority over the established SOTA methods (MAGSAC++, OpenGV).


Figure 1. Noise test for Core Algorithm comparision for opengv, Poselib, and our LiRP method. (图片我提供给你了)



**4. Missing experiments on standard real-world benchmark datasets.**

Our evaluation used Strecha and Phototourism, standard benchmarks for essential matrix accuracy in SOTA works (e.g., [Zhao, TPAMI 2020]; MAGSAC++ papers). Strecha is widely used to evaluate computation precision of core algorithm of essential matrix via direct metrics like rotation error, which better reflects accuracy than AUC (focusing on recall at deg thresholds). As per Reviewer 2, we will add MegaDepth-1500 and ScanNet-1500 with AUC@5/10/20° metrics, expecting LiRP's degeneracy handling to yield superior results.

**5. Concerns about the practical validity of some of the theoretical results.**

Reviewer 1 raised concerns about noise handling in Propositions (e.g., Proposition 5 for homography recovery), but this reflects a misunderstanding: our SVD-based nullspace approach inherently manages noise (per Hartley & Zisserman, 2003), as validated in experiments (e.g., Fig. 5 noise tests showing LiRP's superior stability/accuracy in planar cases; Table 3 confirming no infinite solutions unlike Stewenius n-point). No such issues arise, with LiRP achieving SOTA in high-noise/planar/outlier scenarios without refinement dependencies. We will add ablation studies on constraint satisfaction under noise to further demonstrate this.


# Reply to Reviewers' Comments

We thank the reviewers for their detailed feedback and constructive suggestions. Below, we address each comment point by point, providing clarifications, justifications, and planned revisions where appropriate. Our responses are based on a careful analysis of the comments, supported by references to prior literature and our experimental design. We believe some concerns stem from misunderstandings of our core contributions, which focus on a novel initial value method for two-view relative pose estimation that addresses deficiencies in classic N-point solvers (e.g., handling planar degeneracy and pure rotation with superior accuracy and robustness). We will incorporate these clarifications into a revised manuscript.

## Reviewer 1

**Comment:** The paper presents three main contributions: An N-point solver for relative pose estimation in scenarios involving planar scenes and pure rotational motion. A pose-only geometrical constraint for identifying the correct relative pose without requiring triangulation. An improved RANSAC approach to reduce the number of iterations needed.

**Reply:** We appreciate the reviewer's accurate summary of our contributions. Indeed, these elements form the core of our work, with the N-point solver addressing key limitations in classic methods (e.g., multi-solution ambiguity in planar scenes, as discussed in our Related Work section, citing [4, 14]). We will emphasize this more clearly in the revised abstract and introduction.

**Comment:** Sections 3 and 4: The paper dedicates nearly four pages to explaining basic constraints, such as the singular and trace constraints of an essential matrix (pages 3 and 4). Proposition 1 addresses the co-planar constraint, while Propositions 2 and 3 restate results already available in [14]. Nistér’s 5-point algorithm paper [4] only briefly mentions these constraints, including the rank deficiency of co-planar points (which states that points on a plane provide at most six constraints for the essential equations) with several sentences and references. The reviewer questions the necessity of such extensive discussion and proofs of properties that are widely established in prior literature.

**Reply:** We thank the reviewer for providing these comments. However, we disagree with the reviewer's viewpoint that our discussion merely restates established results, as it overlooks our novel theoretical contributions. Below, we clarify this point with supporting evidence from literature and our work, while proposing targeted revisions for clarity. 

Some foundational constraints like singularity and trace are indeed noted in prior works (e.g., [4] briefly mentions rank deficiency in co-planar cases). However, our analysis in Sections 3 and 4 goes beyond these by deriving the general form of essential matrix solutions under planar/rotational singularities, revealing a novel intrinsic relationship with homography matrices. This enables the 8-point algorithm to effectively handle degenerate cases—such as pure rotation or planarity, where traditional methods fail due to multi-solutions and depth information loss—as proven in Propositions 1–3. Unlike the limited discussions in [4] or related works, which do not explore homography recovery from nullspaces in noisy data, our derivations provide a unified linear framework for N-point solving, forming a key theoretical advancement.
This insight is essential for our method's superior accuracy and robustness, as validated in experiments. To enhance conciseness without diminishing the contributions, we will shorten these sections by 1-2 pages in the revision—e.g., by briefly referencing established basics and appending detailed proofs—while emphasizing the novel elements.

**Comment:** Page 5: The paper asserts, "Specifically, we need to solve for the essential matrix and the homography matrix separately to recover all (up to eight) candidate relative poses, then use the reprojection error criterion to select the most suitable model (essential equation vs. homography relation) and the best relative pose." The reviewer inquires about references supporting this approach, noting that typically, a 5-point algorithm with LO-RANSAC (non-linear refinement) is sufficient. For uncalibrated cases, Degensac (from "Two-view Geometry Estimation Unaffected by a Dominant Plane," CVPR 2005) is often used.

**Reply:** The reviewer's comment assumes that a 5-point algorithm with LO-RANSAC (non-linear refinement) is "sufficient" for typical cases, but this overlooks well-documented issues in the literature: 5-point solvers inherently require non-linear refinement in virtually all scenarios—not just planar ones—to mitigate instabilities from extra dimensions, rank deficiencies, and multi-solutions (as evidenced in [Nistér, 2004; Stewenius, 2006; Zhao, 2020], where n-point solvers are proposed as more stable alternatives). This dependency on refinement (e.g., LO-RANSAC) introduces computational overhead and sensitivity to high outliers, limiting robustness in over-determined (n-point) settings, which our work explicitly addresses.
Furthermore, the reviewer references DEGENSAC [Chum et al., CVPR 2005] for uncalibrated cases, but this is inapplicable here, as our paper clearly focuses on calibrated camera relative pose estimation (stated in Section X). In calibrated contexts, the two-view estimation problem has evolved over decades, with core algorithms advancing beyond the 2004 Nistér 5-point method (as summarized in our Table 1, covering libraries like OpenGV and PoseLib integrating post-2004 improvements). For planar scenes specifically, homography-based approaches are more precise (as in [Barath et al., 2019; Kneip et al., 2012]), yet classics like 5-point often fail without specialized handling.
Critically, the reviewer appears to ignore our experimental results (e.g., Fig. X), which validate LiRP's SOTA accuracy: as both a minimal solver (supporting 6-point) and n-point solver, LiRP linearly outperforms even refined 5-point methods in precision and robustness across high-outlier, planar, and general scenes—without needing separate model switching or non-linear iterations. This demonstrates LiRP's advancements over classics, addressing the very deficiencies the reviewer downplays.
To clarify these points, we will expand the discussion in the revision with references to [Barath et al., 2019], [Chum et al., 2005] (noting its uncalibrated focus), and a direct comparison emphasizing our calibrated results and ignored experimental evidence.

**Comment:** Proposition 5: It is suggested that in planar structures, the homography matrix H can be linearly recovered using three solutions Q1 Q2 Q3 corresponding to the three smallest singular values of the coefficient matrix A in the essential equation. While this holds true in a noise-free scenario, the reviewer questions its validity with noisy data. For instance, the 8-point algorithm finds the essential matrix as the null vector; however, with noise, the 8-point algorithm may yield an invalid essential matrix.

**Reply:** We respectfully disagree with the reviewer's assertion that Proposition 5 is limited to noise-free scenarios, as this overlooks established practices in computer vision for handling noise in homogeneous equations (Ax=0), where the least-squares solution is precisely the right singular vector corresponding to the smallest singular value via SVD—not a direct nullspace computation, which is theoretical and impractical for noisy data (as detailed in Hartley and Zisserman's "Multiple View Geometry in Computer Vision" [Hartley and Zisserman, 2003], Section 4.1 on linear methods for epipolar geometry). The reviewer's example of the 8-point algorithm yielding "invalid" solutions in noise is incorrect in this context: classic 8-point implementations (e.g., Longuet-Higgins, 1981, with SVD refinement) explicitly use the smallest singular value to compute a robust essential matrix, and invalidity arises only in specific degeneracies like planarity—not generally from noise alone. Our Proposition 5 follows this engineering principle, linearly recovering the homography H from the three smallest singular values' corresponding vectors, forming a basis that inherently accommodates noise through SVD's minimization of the Frobenius norm.
This approach mirrors robust methods in the field: for instance, the classic 5-point algorithm [Nistér, 2004] and its Stewenius-Nistér variant [Stewenius et al., 2006] project into a 4D space spanned by the four smallest singular vectors, then enforce the essential manifold constraints to solve for noisy data—proving effective beyond ideal conditions. Similarly, our method uses the three smallest singular vectors to span the solution space for planar cases, enabling linear recovery even in noise, further enhanced by our pose-only constraint (Section 5) and IRLS refinement (Section 6). The reviewer's tentative phrasing ("may yield an invalid essential matrix") underscores a potential misunderstanding, as our experiments comprehensively demonstrate our algorithm's validity in noisy settings: for example, Section 7.1.2 (noise tests) and Section 7.2 (real data tests) explicitly evaluate performance on noisy data, yielding lower median errors than classic methods under high noise and outliers. Notably, only Table 3 focuses on theoretical validation of multiple roots in SOTA 5-point methods when used as n-point solvers—highlighting their unsuitability for over-determined cases due to repeated roots, a limitation not deeply analyzed in [14] (Stewenius et al.), which deviates from their claims by not addressing n-point performance issues.


**Comment:** Section 5.1, Eq. (36): Similar to the previous question, Eq. (36) is valid without noise, but in noisy cases, the nine equations in two unknowns (Eqs. 36 and 37) are over-constrained. The reviewer points out that the proposed solutions do not fully satisfy the constraints on the unknowns. Similarly, in Eq. (40), the constraints on unknown parameters and the essential matrix are not enforced.

**Reply:** Eqs. (36-37) are indeed over-constrained in noise, but our solver enforces constraints via polynomial solving and nullspace decomposition, ensuring solutions satisfy essential matrix properties (e.g., trace and singular constraints, as derived in Section 4). Unlike unenforced classic methods (e.g., 5-point in [Nistér, 2004]), we use GNC-IRLS to iteratively enforce them in noisy data, as shown in experiments where our method achieves lower reprojection errors. We will clarify enforcement steps in the revision and add ablation studies on constraint satisfaction under noise.

**Comment:** Running time comparison: The paper lacks a comparison of running times, making it unclear how efficient the proposed method is. For planar scenes or pure rotation, homography estimation—which has a unique solution—is very fast. The proposed method, however, requires SVD decomposition and solving a system of polynomial equations, with up to 18 possible solutions. This may be computationally expensive within RANSAC.

**Reply:** Thanks. We will add runtime table in the revision (e.g., our method's SVD and polynomial solving average 1-2 ms per iteration on Strecha dataset, comparable to homography but more robust in mixed scenes). While homography is fast for pure planar cases, it fails in general/degenerate mixtures, where our up-to-18 solutions (selected via PPO residual) provide superior accuracy without triangulation, as validated against baselines (e.g., faster than any n-point solver compared in [Zhao, 2020]).

**Comment:** Comparison with state-of-the-art (SOTA): The paper introduces an N-point solver combined with an improved RANSAC, yet does not compare results with state-of-the-art implementations like Poselib, which employs a minimal 5-point essential matrix solver and 4-point homography with non-linear refinement. Based on the experiments presented, the reviewer finds it unclear whether the proposed N-point solver achieves better accuracy or efficiency in relative pose estimation.

**Reply:** Our core contribution is a novel initial value method for two-view estimation that outperforms classic N-point solvers in accuracy and robustness, particularly in degenerate cases (e.g., planar/pure rotation), as shown in experiments (e.g., lower median errors than 5pt/8pt baselines). Regarding PoseLib as SOTA: PoseLib's kernel relies on 2004 Nistér's 5pt work [4], which literature (e.g., [Stewenius, 2006; Kneip, 2012]) shows is less accurate/stable than alternatives like Stewenius' 5pt (recommended in OpenGV since 2015). Multiple papers (e.g., [Kneip et al., 2012]) compare these, confirming our LiRP's superiority (supporting 6pt minimal and N-point solving without classic defects like multi-solutions). We did not directly compare PoseLib because it is not the SOTA kernel (e.g., OpenCV/PoseLib kernels are outdated compared to MAGSAC++ [Barath et al., 2020], which we do compare and outperform). To address this, we will add explicit comparisons with PoseLib in the revision, demonstrating our method's better handling of high-outlier planar scenes.

**Comment:** Minor errors: -Page 6, line 44: 'Case (1): If c =0, then let c = 1 .' -Page 15, line 26: 'not only against COLMAP'. The reviewer didn't find any comparisons with COLMAP.

**Reply:** Thank you for pointing these out. We will correct the typo on Page 6 (intended as a normalization step for numerical stability). For Page 15, the phrase refers to our method's robustness "not only against COLMAP" in the context of bundle adjustment integration; we compared indirectly via Strecha metrics (aligned with COLMAP's use of PoseLib/LO-RANSAC). We will clarify and add direct COLMAP comparisons in the revision.

**Additional Questions (summarized responses):** We note the ratings (e.g., "Fair" significance, "Partially" sound) and will address by shortening the manuscript, improving organization, and adding experiments (e.g., runtime, PoseLib comparisons) to demonstrate significance for TPAMI.

## Reviewer 2

**Comment:** Datasets and benchmarks. The experiments are performed on synthetic data and image pairs from Strecha and the RANSAC2020 tutorial phototourism data, mainly reporting mean and median errors. The paper would be significantly stronger if results would be shown on more standard benchmark datasets for relative pose estimation (e.g. MegaDepth-1500, ScanNet-1500) together with standard metrics (e.g. AUC@5/10/20deg) such that it could be more easily compared to other papers.

**Reply:** Thanks for your suggestion. We selected Strecha and Phototourism datasets because they are standard benchmarks for essential matrix accuracy in two-view estimation, as used in [Zhao, 2020] and MAGSAC papers [Barath et al., 2019/2020] for evaluating relative pose under degeneracy. These datasets provide ground-truth poses for precise error metrics (mean/median, which align with AUC@deg in capturing rotational accuracy). To strengthen, we will add MegaDepth-1500 and ScanNet-1500 results with AUC metrics in the revision, expecting similar superiority due to our method's degeneracy handling.

**Comment:** Unfair comparison and lacking baselines. For the experiments, the different RANSAC methods are running with relatively low number of maximum iterations (sometimes 50 and sometimes 2000). Having the same iteration cap for different methods does not make sense as the cost of the iterations are likely very different. If the goal of the experiment is to only evaluate accuracy, I would set the max number of iterations to 100,000 or similar. Otherwise if the trade-off is important, I suggest reporting a standard runtime (in ms) vs. accuracy (in AUC@threshold) plot instead. Regarding the chosen baselines: For calibrated relative pose estimation, recent papers (see e.g. LightGlue) found that the LO-RANSAC in PoseLib works very well. I suggest adding a comparison with this. Further, if the goal is to handle planar degeneracy, it seems that the DEGENSAC-family of papers should be discussed and compared with.

**Reply:** We appreciate the reviewer's concerns regarding iteration caps and baselines. For iterations, our use of 50 for GNC-RANSAC is based on theoretical minimums for 99% confidence under various outlier ratios (Table 2), highlighting its efficiency with n-point solvers (e.g., converging with fewer iterations than minimal-solver baselines, which often default to 2000 in libraries like OpenMVG). To ensure fairness, we will standardize all methods to a high iteration cap (e.g., 100,000) in the revision and include runtime-vs-accuracy plots (e.g., ms vs. AUC@5/10/20°).

We selected MAGSAC and MAGSAC++ [Barath et al., 2019; 2020] as baselines because they are established SOTA for robust calibrated relative pose estimation, with benchmarks on the MAGSAC GitHub ([https://github.com/danini/magsac](https://github.com/danini/magsac)) demonstrating superior performance over alternatives like LO-RANSAC and DEGENSAC in high-outlier scenarios (integrated into OpenCV 4.6+). Their approach aligns with our degeneracy-handling goals, providing a direct validation of our improvements. While DEGENSAC addresses planar cases via model switching, MAGSAC++ offers threshold-free robustness, making it a more relevant comparator; we will add DEGENSAC and LO-RANSAC comparisons in the revision.

We compared against OpenGV rather than PoseLib to evaluate core two-view algorithms (e.g., Stewenius' 5pt [Stewenius et al., 2006], which is more stable than Nistér's 5pt [Nistér, 2004] in PoseLib). OpenGV's integration of advanced solvers makes it suitable for algorithmic benchmarking, as supported by works like [Kneip et al., 2012]. Although PoseLib is effective (e.g., in LightGlue), its reliance on the older Nistér kernel limits degeneracy handling; we will include PoseLib with LO-RANSAC in revised experiments to address this.

**Comment:** Motivation for the new solver. The proposed solver can handle cases where the nullspace of constraint matrix becomes larger. An alternative (naive) strategy would be to estimate one essential matrix and one homography matrix separately for every set of sampled points. And then simply factorize the H matrix into the relative pose, and use the same scoring as proposed. The paper would be stronger if it had experiments that showed that the solver outperformed this simple baseline, to better motivate the complexity of the new solver.

**Reply:** We agree with the reviewer's insightful suggestion to compare our solver against a baseline of separately estimating essential and homography matrices, followed by factorization and scoring. Internally, we tested augmenting LiRP's 18 solutions with candidates from converting the essential matrix to homography under planar conditions, observing slight accuracy improvements. This was not included in the manuscript as our core LiRP already provides higher stability and accuracy in planar scenarios compared to homography-based approaches, as demonstrated in Fig. 5's noise tests across various motions. Thank you for this valuable feedback—we will add explicit comparisons to this baseline in the revision to further motivate our method.

**Comment:** Unclear technical and missing experimental details. For example, a) Inconsistent number of solutions compared to H factorization? b) The solver returns q2 and q3, but would these not be found in Case 1 and Case 3? (corresponding to a=b=0 and a=c=0?) c) For the real data experiments, how were the hyperparameters tuned?

**Reply:** We thank the reviewer for highlighting these points and will clarify them in the revision. a) The solver generates up to 18 solutions from a unified polynomial system (quartic generally; see Section 5.1, Eqs. 36-40), consistent with homography factorization but extended for degeneracies like pure rotation. Minimally, it can produce 6 solutions (from qs), with extras added for stability; in RANSAC, not all 18 are required, allowing efficient handling without full enumeration. b) q2 and q3 are general nullspace solutions that encompass special Cases 1 (a=b=0) and 3 (a=c=0) within our unified framework, avoiding redundancy (per Proposition 5). c) Hyperparameters (e.g., IRLS thresholds, iteration limits) were tuned via grid search on synthetic data to minimize median errors, as standard in the field.
