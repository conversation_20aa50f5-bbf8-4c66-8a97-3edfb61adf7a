Dataset,<PERSON>,<PERSON>_<PERSON><PERSON><PERSON>,<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>30,<PERSON><PERSON>(50),0.40,0.12
<PERSON>-P30,<PERSON><PERSON><PERSON>(2000),1.14,0.54
Castle-P30,<PERSON><PERSON><PERSON>(2000),1.65,0.75
Castle-<PERSON>30,<PERSON><PERSON><PERSON><PERSON>(20000),24.55,11.56
Castle-<PERSON>19,<PERSON><PERSON>(50),0.13,0.07
<PERSON>-<PERSON>19,<PERSON><PERSON><PERSON>(2000),0.64,0.21
Castle-P19,<PERSON><PERSON><PERSON>(2000),1.05,0.71
Castle-P19,<PERSON><PERSON><PERSON><PERSON>(20000),21.09,8.06
Entry-P10,<PERSON><PERSON>(50),0.04,0.04
Entry-P10,OpenCV(2000),0.53,0.22
Entry-P10,<PERSON>GV(2000),0.46,0.35
Entry-P10,<PERSON><PERSON><PERSON><PERSON>(20000),19.04,9.00
Fountain-P11,<PERSON><PERSON>(50),0.06,0.04
Fountain-P11,<PERSON><PERSON><PERSON>(2000),0.08,0.05
<PERSON>-P11,<PERSON><PERSON><PERSON>(2000),0.44,0.35
<PERSON>-<PERSON>11,<PERSON><PERSON><PERSON><PERSON>(20000),15.15,6.00
<PERSON><PERSON>-<PERSON>-<PERSON><PERSON>,<PERSON><PERSON>(50),0.10,0.07
<PERSON><PERSON><PERSON><PERSON>-<PERSON>25,<PERSON><PERSON><PERSON>(2000),0.25,0.11
<PERSON><PERSON><PERSON><PERSON>-<PERSON>25,<PERSON><PERSON><PERSON>(2000),0.54,0.39
<PERSON><PERSON>-<PERSON>-<PERSON>25,<PERSON><PERSON><PERSON><PERSON>(20000),23.71,14.97
<PERSON>z-<PERSON>-<PERSON>8,<PERSON><PERSON>(50),0.07,0.06
<PERSON><PERSON>-<PERSON>-<PERSON>8,<PERSON><PERSON><PERSON>(2000),0.07,0.06
<PERSON><PERSON>-<PERSON>-<PERSON>8,<PERSON><PERSON>V(2000),0.44,0.36
<PERSON><PERSON>-<PERSON>-<PERSON>8,<PERSON><PERSON><PERSON>ib(20000),15.65,10.19
