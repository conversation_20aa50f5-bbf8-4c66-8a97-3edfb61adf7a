{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/Cellar/cmake/3.31.5/bin/cmake", "cpack": "/opt/homebrew/Cellar/cmake/3.31.5/bin/cpack", "ctest": "/opt/homebrew/Cellar/cmake/3.31.5/bin/ctest", "root": "/opt/homebrew/Cellar/cmake/3.31.5/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 5, "string": "3.31.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-32383a8b3935dc806b1c.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-0f0a25cd7d1bd45e24ca.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-a1cb8f810901678fa9d4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-0f0a25cd7d1bd45e24ca.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-a1cb8f810901678fa9d4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-32383a8b3935dc806b1c.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}