/**
 * @file method_TwoViewOptimizer.cpp
 * @brief 双视图位姿优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_TwoViewOptimizer.hpp"
#include "TwoViewOptimizerBase.hpp"
#include "EigenLMOptimizer.hpp"
#include "CeresOptimizer.hpp"
#include "interfaces_robust_estimator.hpp"
#include <boost/algorithm/string.hpp>
#include <iostream>
#include <chrono>

namespace PoSDK
{

    MethodTwoViewOptimizer::MethodTwoViewOptimizer()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>
        required_package_["data_map"] = nullptr;    // RelativePose初始估计

        // 初始化默认配置路径
        InitializeDefaultConfigPath();
    }

    Interface::DataPtr MethodTwoViewOptimizer::Run()
    {
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer started" << std::endl;
            DisplayConfigInfo();
        }

        // 1. 验证输入数据
        if (!ValidateInputData())
        {
            PO_LOG_ERR << "Input data validation failed" << std::endl;
            return nullptr;
        }

        // 2. 从data_map获取初始位姿估计
        auto pose_data = required_package_["data_map"];
        auto initial_pose_ptr = GetDataPtr<RelativePose>(pose_data);
        if (!initial_pose_ptr)
        {
            PO_LOG_ERR << "Failed to get initial RelativePose data" << std::endl;
            return nullptr;
        }
        RelativePose optimized_pose = *initial_pose_ptr;

        // 3. 转换bearing pairs到bearing vectors
        BearingVectors points1, points2;
        if (!ConvertBearingPairsToBearingVectors(points1, points2))
        {
            PO_LOG_ERR << "Failed to convert bearing pairs" << std::endl;
            return nullptr;
        }

        // 4. 检查是否有权重信息
        VectorXd *weights_ptr = nullptr;
        VectorXd weights;
        if (!prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
        {
            auto weights_data = std::dynamic_pointer_cast<Interface::DataMap<VectorXd>>(prior_info_["weights"]);
            if (weights_data && weights_data->GetMapPtr())
            {
                weights = *(weights_data->GetMapPtr());
                weights_ptr = &weights;
                PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
            }
        }

        // 5. 执行位姿优化（记录核心计算时间）
        auto core_start_time = std::chrono::high_resolution_clock::now();

        if (!OptimizeRelativePose(points1, points2, optimized_pose, weights_ptr))
        {
            PO_LOG_ERR << "Failed to optimize relative pose" << std::endl;
            // 优化失败时返回nullptr，避免结果混淆
            return nullptr;
        }

        // 记录核心计算结束时间
        auto core_end_time = std::chrono::high_resolution_clock::now();
        double core_time_ms = std::chrono::duration<double, std::milli>(core_end_time - core_start_time).count();

        // 设置核心计算时间（在日志输出之前，确保时间测量准确性）
        SetCoreTime(core_time_ms);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "TwoViewOptimizer core computation time: " << core_time_ms << " ms" << std::endl;
        }

        // 6. 更新DataSample状态（优化后所有点都被认为是内点）
        auto sample_ptr = Interface::CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (sample_ptr && !sample_ptr->empty())
        {
            auto inliers_ptr = std::make_shared<std::vector<size_t>>();
            inliers_ptr->reserve(sample_ptr->size());
            for (size_t i = 0; i < sample_ptr->size(); ++i)
            {
                inliers_ptr->push_back(i);
            }
            sample_ptr->SetBestInliers(inliers_ptr);
        }

        // 7. 返回优化后的位姿
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer completed successfully" << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized rotation matrix:\n"
                                  << optimized_pose.Rij << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized translation vector:\n"
                                  << optimized_pose.tij << std::endl;
        }

        return std::make_shared<Interface::DataMap<RelativePose>>(optimized_pose);
    }

    bool MethodTwoViewOptimizer::ValidateInputData()
    {
        auto sample_data = required_package_["data_sample"];
        auto pose_data = required_package_["data_map"];

        if (!sample_data || !pose_data)
        {
            PO_LOG_ERR << "Missing required input data" << std::endl;
            return false;
        }

        auto bearing_pairs_ptr = GetDataPtr<BearingPairs>(sample_data);
        if (!bearing_pairs_ptr || bearing_pairs_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs data" << std::endl;
            return false;
        }

        if (bearing_pairs_ptr->size() < kMinNumPoints)
        {
            PO_LOG_ERR << "Insufficient points for optimization: got "
                       << bearing_pairs_ptr->size() << ", need at least " << kMinNumPoints << std::endl;
            return false;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::ConvertBearingPairsToBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2) const
    {
        auto sample_ptr = Interface::CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (!sample_ptr || sample_ptr->empty())
        {
            return false;
        }

        const size_t num_points = sample_ptr->size();
        points1.resize(3, num_points);
        points2.resize(3, num_points);

        size_t i = 0;
        for (const auto &bearing_pair : *sample_ptr)
        {
            points1.col(i) = bearing_pair.template head<3>();
            points2.col(i) = bearing_pair.template tail<3>();
            ++i;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::OptimizeRelativePose(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights)
    {
        std::string optimizer_type = GetOptionAsString("optimizer_type", "eigen_lm");
        std::string residual_type = GetResidualType();
        std::string loss_type = GetLossType();

        // 检查优化器和残差类型的兼容性
        if ((residual_type == "ba" || residual_type == "opengv") &&
            boost::iequals(optimizer_type, "eigen_lm"))
        {
            PO_LOG_ERR << "错误: EigenLM优化器不支持Bundle Adjustment类型的残差 (" << residual_type << ")" << std::endl;
            PO_LOG_ERR << "Bundle Adjustment需要稀疏Schur分解，请使用Ceres优化器" << std::endl;
            PO_LOG_ERR << "建议配置: optimizer_type=ceres, residual_type=" << residual_type << std::endl;
            return false;
        }

        auto optimizer = CreateOptimizer(optimizer_type);

        if (!optimizer)
        {
            PO_LOG_ERR << "Failed to create optimizer: " << optimizer_type << std::endl;
            return false;
        }

        // 设置优化器参数
        optimizer->SetMaxIterations(GetOptionAsIndexT("max_iterations", 50));
        optimizer->SetConvergenceThreshold(GetOptionAsFloat("convergence_threshold", 1e-8));
        optimizer->SetReprojThetaThreshold(GetReprojThetaThresholdDeg());
        optimizer->SetHuberThresholdExplicit(GetHuberThresholdExplicit());
        optimizer->SetCauchyThresholdExplicit(GetCauchyThresholdExplicit());
        optimizer->SetLogLevel(log_level_);

        // 设置迭代信息显示
        bool enable_iter_display = GetOptionAsBool("iter_echo_on", false);
        optimizer->SetIterInfo(enable_iter_display);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Using optimizer: " << optimizer_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using residual: " << residual_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using loss function: " << loss_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Optimizer-Residual compatibility: "
                                   << ((residual_type == "ba" || residual_type == "opengv") ? "Bundle Adjustment" : "Pose-only") << std::endl;
        }

        bool success = optimizer->Optimize(points1, points2, pose, weights, residual_type, loss_type);

        if (!success)
        {
            PO_LOG_ERR << "优化失败: " << optimizer_type << " + " << residual_type << " + " << loss_type << std::endl;
            if ((residual_type == "ba" || residual_type == "opengv") &&
                boost::iequals(optimizer_type, "eigen_lm"))
            {
                PO_LOG_ERR << "提示: Bundle Adjustment类型残差请使用Ceres优化器" << std::endl;
            }
        }

        return success;
    }

    std::string MethodTwoViewOptimizer::GetResidualType() const
    {
        return GetOptionAsString("residual_type", "ppo_opengv");
    }

    std::string MethodTwoViewOptimizer::GetLossType() const
    {
        return GetOptionAsString("loss_type", "huber");
    }

    double MethodTwoViewOptimizer::GetReprojThetaThresholdDeg() const
    {
        return GetOptionAsFloat("reproj_theta_threshold_deg", 0.57);
    }

    double MethodTwoViewOptimizer::GetHuberThresholdExplicit() const
    {
        return GetOptionAsFloat("huber_threshold_explicit", -1.0);
    }

    double MethodTwoViewOptimizer::GetCauchyThresholdExplicit() const
    {
        return GetOptionAsFloat("cauchy_threshold_explicit", -1.0);
    }

    std::unique_ptr<TwoViewOptimizerBase> MethodTwoViewOptimizer::CreateOptimizer(const std::string &optimizer_type)
    {
        if (boost::iequals(optimizer_type, "eigen_lm"))
        {
            return std::make_unique<EigenLMOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "ceres"))
        {
            return std::make_unique<CeresOptimizer>();
        }
        else
        {
            PO_LOG_ERR << "Unknown optimizer type: " << optimizer_type << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Available optimizers: eigen_lm, ceres" << std::endl;
            return nullptr;
        }
    }

} // namespace PoSDK
