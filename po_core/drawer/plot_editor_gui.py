#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# @ author: <PERSON>
# @ copyright: VinfSTJU Group, 2025
# @ version: 1.1.0

"""
matplotlib图表编辑器 - GUI版本

这个程序提供一个图形界面来编辑保存在.pkl文件中的matplotlib图表。
支持修改：
- 线条颜色和透明度
- 线条宽度和样式
- 标记类型和大小
- 图表标题和标签
- 图例设置
- 图层显示顺序调整
- 批量样式保存和应用
- 工程样式导入导出

版本更新：
v1.1.0 (2025-01-15)
- 新增图层顺序调整功能
- 添加"保存样式"快捷按钮
- 修复macOS下GUI渲染问题
- 优化重绘机制和用户体验
- 支持拖拽调整算法显示层级
"""

import sys
import pickle
import numpy as np
import json
import os
import platform
from pathlib import Path
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import hashlib
import tempfile
import shutil
from datetime import datetime
import copy

# macOS兼容性优化
if platform.system() == 'Darwin':
    # macOS特定设置
    matplotlib.use('Qt5Agg')  # 确保使用Qt5Agg后端
    
# 兼容不同版本的matplotlib后端导入
try:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
except ImportError:
    try:
        from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.backends.backend_qt import NavigationToolbar2QT as NavigationToolbar
    except ImportError:
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    
    # macOS高DPI支持
    if platform.system() == 'Darwin':
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
except ImportError:
    print("需要安装PyQt5: pip install PyQt5")
    sys.exit(1)

try:
    import pandas as pd
    import seaborn as sns
except ImportError:
    print("需要安装pandas和seaborn: pip install pandas seaborn")
    pd = None
    sns = None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_file = os.path.expanduser("~/.plot_editor_config.json")
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            "last_directory": os.path.expanduser("~"),
            "algorithm_styles": {}
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_last_directory(self):
        """获取上次打开的目录"""
        return self.config.get("last_directory", os.path.expanduser("~"))
    
    def set_last_directory(self, directory):
        """设置上次打开的目录"""
        self.config["last_directory"] = directory
        self.save_config()
    
    def save_algorithm_style(self, algorithm_name, style_dict):
        """保存算法样式"""
        self.config["algorithm_styles"][algorithm_name] = style_dict
        self.save_config()
    
    def get_algorithm_style(self, algorithm_name):
        """获取算法样式"""
        return self.config["algorithm_styles"].get(algorithm_name, None)
    
    def get_all_algorithm_styles(self):
        """获取所有算法样式"""
        return self.config["algorithm_styles"]


class LegendEditDialog(QDialog):
    """图例编辑对话框"""
    
    def __init__(self, ax, parent=None):
        super().__init__(parent)
        self.ax = ax
        self.legend = ax.get_legend()
        self.original_labels = []
        self.line_edits = []
        self.setupUI()
        self.setWindowTitle("编辑图例名称")
        self.resize(500, 400)
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 说明文本
        info_label = QLabel("编辑下方的算法名称，修改后将应用到图例和线条标签：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        # 获取当前图例文本
        if self.legend:
            legend_texts = self.legend.get_texts()
            lines = self.ax.lines
            
            for i, (text_obj, line) in enumerate(zip(legend_texts, lines)):
                original_label = text_obj.get_text()
                self.original_labels.append(original_label)
                
                # 创建编辑行
                row_widget = QWidget()
                row_layout = QHBoxLayout()
                row_layout.setContentsMargins(5, 5, 5, 5)
                
                # 颜色预览
                color_preview = QLabel()
                color_preview.setFixedSize(20, 20)
                try:
                    color = line.get_color()
                    rgb = mcolors.to_rgb(color)
                    r, g, b = [int(c * 255) for c in rgb]
                    color_preview.setStyleSheet(f"background-color: rgb({r}, {g}, {b}); border: 1px solid black;")
                except:
                    color_preview.setStyleSheet("background-color: gray; border: 1px solid black;")
                
                # 原始名称标签
                original_label_widget = QLabel(f"原始: {original_label}")
                original_label_widget.setMinimumWidth(150)
                original_label_widget.setStyleSheet("QLabel { color: #666; font-size: 10px; }")
                
                # 编辑框
                line_edit = QLineEdit(original_label)
                line_edit.setMinimumWidth(200)
                self.line_edits.append(line_edit)
                
                # 规范化按钮
                normalize_btn = QPushButton("规范化")
                normalize_btn.setMaximumWidth(60)
                normalize_btn.clicked.connect(lambda checked, edit=line_edit: self.normalizeLineEdit(edit))
                
                row_layout.addWidget(color_preview)
                row_layout.addWidget(original_label_widget)
                row_layout.addWidget(line_edit)
                row_layout.addWidget(normalize_btn)
                row_layout.addStretch()
                
                row_widget.setLayout(row_layout)
                scroll_layout.addWidget(row_widget)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        # 全部规范化按钮
        normalize_all_btn = QPushButton("全部规范化")
        normalize_all_btn.clicked.connect(self.normalizeAll)
        button_layout.addWidget(normalize_all_btn)
        
        button_layout.addStretch()
        
        # 确定和取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def normalizeLineEdit(self, line_edit):
        """规范化单个编辑框的文本：只有第一个单词首字母大写"""
        text = line_edit.text()
        if not text:
            return
        
        # 将下划线替换为空格
        normalized = text.replace('_', ' ')
        
        # 将整个字符串转为小写，然后只将第一个字符大写
        if normalized:
            normalized = normalized.lower()
            normalized = normalized[0].upper() + normalized[1:]
        
        line_edit.setText(normalized)
    
    def normalizeAll(self):
        """规范化所有编辑框的文本"""
        for line_edit in self.line_edits:
            self.normalizeLineEdit(line_edit)
    
    def accept(self):
        """确定按钮处理"""
        # 应用新的标签名称
        if self.legend:
            legend_texts = self.legend.get_texts()
            lines = self.ax.lines
            
            for i, (text_obj, line, line_edit) in enumerate(zip(legend_texts, lines, self.line_edits)):
                new_label = line_edit.text().strip()
                if new_label:
                    # 更新图例文本
                    text_obj.set_text(new_label)
                    # 更新线条标签
                    line.set_label(new_label)
        
        super().accept()


class BatchProcessDialog(QDialog):
    """批处理对话框"""
    
    def __init__(self, algorithm_styles, parent=None):
        super().__init__(parent)
        self.algorithm_styles = algorithm_styles
        self.selected_files = []
        self.selected_directory = ""
        self.setupUI()
        self.setWindowTitle("批处理设置")
        self.resize(600, 400)
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 选择模式
        mode_group = QGroupBox("处理模式")
        mode_layout = QVBoxLayout()
        
        self.file_mode_btn = QPushButton("选择多个文件")
        self.file_mode_btn.clicked.connect(self.selectFiles)
        mode_layout.addWidget(self.file_mode_btn)
        
        self.dir_mode_btn = QPushButton("选择目录（处理所有.pkl文件）")
        self.dir_mode_btn.clicked.connect(self.selectDirectory)
        mode_layout.addWidget(self.dir_mode_btn)
        
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # 选择结果显示
        result_group = QGroupBox("选择结果")
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(100)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 样式预览
        style_group = QGroupBox("将要应用的样式")
        style_layout = QVBoxLayout()
        
        self.style_text = QTextEdit()
        self.style_text.setReadOnly(True)
        self.updateStylePreview()
        style_layout.addWidget(self.style_text)
        
        style_group.setLayout(style_layout)
        layout.addWidget(style_group)
        
        # 导出选项
        export_group = QGroupBox("导出选项")
        export_layout = QVBoxLayout()
        
        self.export_png_check = QCheckBox("同时导出PNG文件")
        self.export_png_check.setChecked(True)  # 默认勾选
        self.export_png_check.setToolTip("处理完PKL文件后，自动导出对应的PNG图片文件")
        export_layout.addWidget(self.export_png_check)
        
        self.export_pdf_check = QCheckBox("同时导出PDF文件")
        self.export_pdf_check.setChecked(False)  # 默认不勾选
        self.export_pdf_check.setToolTip("处理完PKL文件后，自动导出对应的PDF文档文件")
        export_layout.addWidget(self.export_pdf_check)
        
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def selectFiles(self):
        """选择多个文件"""
        # 尝试从父窗口获取配置管理器
        last_dir = os.path.expanduser("~")
        if hasattr(self.parent(), 'config_manager'):
            last_dir = self.parent().config_manager.get_last_directory()
        
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择.pkl文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
        
        if files:
            self.selected_files = files
            self.selected_directory = ""
            self.result_text.setText(f"选择了 {len(files)} 个文件:\n" + "\n".join(files))
    
    def selectDirectory(self):
        """选择目录"""
        # 尝试从父窗口获取配置管理器
        last_dir = os.path.expanduser("~")
        if hasattr(self.parent(), 'config_manager'):
            last_dir = self.parent().config_manager.get_last_directory()
        
        directory = QFileDialog.getExistingDirectory(self, "选择目录", last_dir)
        
        if directory:
            self.selected_directory = directory
            self.selected_files = []
            
            # 查找目录中的.pkl文件
            pkl_files = list(Path(directory).glob("*.pkl"))
            self.result_text.setText(f"目录: {directory}\n找到 {len(pkl_files)} 个.pkl文件:\n" + 
                                   "\n".join([f.name for f in pkl_files]))
    
    def updateStylePreview(self):
        """更新样式预览"""
        if not self.algorithm_styles:
            self.style_text.setText("没有保存的样式")
            return
        
        preview_text = "算法样式预览:\n\n"
        for algorithm, style in self.algorithm_styles.items():
            preview_text += f"算法: {algorithm}\n"
            for key, value in style.items():
                preview_text += f"  {key}: {value}\n"
            preview_text += "\n"
        
        self.style_text.setText(preview_text)
    
    def getSelectedFiles(self):
        """获取选择的文件列表"""
        if self.selected_files:
            return self.selected_files
        elif self.selected_directory:
            return [str(f) for f in Path(self.selected_directory).glob("*.pkl")]
        else:
            return []
    
    def shouldExportPNG(self):
        """是否应该导出PNG文件"""
        return self.export_png_check.isChecked()
    
    def shouldExportPDF(self):
        """是否应该导出PDF文件"""
        return self.export_pdf_check.isChecked()


class LayerOrderDialog(QDialog):
    """图层顺序调整对话框"""
    
    def __init__(self, ax, parent=None):
        super().__init__(parent)
        self.ax = ax
        self.lines = ax.lines
        self.original_order = list(range(len(self.lines)))
        self.current_order = list(range(len(self.lines)))
        self.setupUI()
        self.setWindowTitle("调整图层顺序")
        self.resize(500, 600)
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 说明文本
        info_label = QLabel("拖拽下方列表来调整算法的图层显示顺序。越靠上的算法显示在越上层：")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin: 10px; }")
        layout.addWidget(info_label)
        
        # 创建算法列表
        self.algorithm_list = QListWidget()
        self.algorithm_list.setDragDropMode(QListWidget.InternalMove)
        self.algorithm_list.setDefaultDropAction(Qt.MoveAction)
        self.algorithm_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #3498db;
                border-radius: 8px;
                background-color: white;
                selection-background-color: #e8f4fd;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px;
                border-radius: 4px;
                border: 1px solid #bdc3c7;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
                border-color: #95a5a6;
            }
        """)
        
        # 填充算法列表
        self.populateAlgorithmList()
        
        # 连接重新排序信号
        self.algorithm_list.itemChanged.connect(self.onOrderChanged)
        
        layout.addWidget(self.algorithm_list)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置顺序")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_btn.clicked.connect(self.resetOrder)
        button_layout.addWidget(reset_btn)
        
        # 上移/下移按钮
        up_btn = QPushButton("上移")
        up_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        up_btn.clicked.connect(self.moveUp)
        button_layout.addWidget(up_btn)
        
        down_btn = QPushButton("下移")
        down_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        down_btn.clicked.connect(self.moveDown)
        button_layout.addWidget(down_btn)
        
        button_layout.addStretch()
        
        # 确定和取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def populateAlgorithmList(self):
        """填充算法列表"""
        self.algorithm_list.clear()
        
        # 获取图例信息
        legend = self.ax.get_legend()
        legend_texts = []
        if legend:
            legend_texts = [text.get_text() for text in legend.get_texts()]
        
        for i, line in enumerate(self.lines):
            # 获取算法名称
            algorithm_name = ""
            if i < len(legend_texts) and legend_texts[i]:
                algorithm_name = legend_texts[i]
            elif line.get_label() and not line.get_label().startswith('_'):
                algorithm_name = line.get_label()
            else:
                algorithm_name = f"Line {i + 1}"
            
            # 创建列表项
            item = QListWidgetItem()
            
            # 创建显示文本，包含颜色信息
            try:
                color = line.get_color()
                rgb = mcolors.to_rgb(color)
                r, g, b = [int(c * 255) for c in rgb]
                color_indicator = f"●"  # 圆点作为颜色指示器
            except:
                color_indicator = "●"
                r, g, b = 128, 128, 128
            
            display_text = f"{color_indicator} {algorithm_name}"
            item.setText(display_text)
            
            # 存储原始索引
            item.setData(Qt.UserRole, i)
            
            # 设置工具提示
            tooltip = f"算法: {algorithm_name}\n颜色: RGB({r}, {g}, {b})\n图层: {i + 1}"
            item.setToolTip(tooltip)
            
            self.algorithm_list.addItem(item)
    
    def onOrderChanged(self):
        """顺序改变时更新current_order"""
        self.current_order = []
        for i in range(self.algorithm_list.count()):
            item = self.algorithm_list.item(i)
            original_index = item.data(Qt.UserRole)
            self.current_order.append(original_index)
    
    def resetOrder(self):
        """重置为原始顺序"""
        self.current_order = list(range(len(self.lines)))
        self.populateAlgorithmList()
    
    def moveUp(self):
        """上移选中项"""
        current_row = self.algorithm_list.currentRow()
        if current_row > 0:
            item = self.algorithm_list.takeItem(current_row)
            self.algorithm_list.insertItem(current_row - 1, item)
            self.algorithm_list.setCurrentRow(current_row - 1)
            self.onOrderChanged()
    
    def moveDown(self):
        """下移选中项"""
        current_row = self.algorithm_list.currentRow()
        if current_row < self.algorithm_list.count() - 1:
            item = self.algorithm_list.takeItem(current_row)
            self.algorithm_list.insertItem(current_row + 1, item)
            self.algorithm_list.setCurrentRow(current_row + 1)
            self.onOrderChanged()
    
    def accept(self):
        """确定按钮处理"""
        # 更新顺序
        self.onOrderChanged()
        
        # 应用新的图层顺序
        if self.current_order != self.original_order:
            self.applyLayerOrder()
        
        super().accept()
    
    def applyLayerOrder(self):
        """应用新的图层顺序"""
        try:
            # 设置z-order，索引越大的线条显示在越上层
            for display_order, original_index in enumerate(self.current_order):
                line = self.lines[original_index]
                # 设置z-order，显示顺序越靠前的z-order越大（显示在上层）
                z_order = len(self.current_order) - display_order
                line.set_zorder(z_order)
            
            # 立即重绘图表以显示层级变化
            if hasattr(self.ax, 'figure') and hasattr(self.ax.figure, 'canvas'):
                canvas = self.ax.figure.canvas
                if canvas:
                    # 强制完整重绘
                    self.ax.figure.canvas.draw()
            
            print(f"应用图层顺序: {self.current_order}")
            
        except Exception as e:
            print(f"应用图层顺序失败: {e}")
    
    def getLayerOrder(self):
        """获取当前图层顺序"""
        return self.current_order


class BatchProcessThread(QThread):
    """批处理线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished_signal = pyqtSignal(int, int)  # 成功数量, 总数量
    
    def __init__(self, files, algorithm_styles, export_png=True, export_pdf=False):
        super().__init__()
        self.files = files
        self.algorithm_styles = algorithm_styles
        self.export_png = export_png
        self.export_pdf = export_pdf
    
    def run(self):
        """运行批处理"""
        success_count = 0
        total_count = len(self.files)
        
        for i, file_path in enumerate(self.files):
            try:
                self.status.emit(f"处理文件: {Path(file_path).name}")
                
                # 加载.pkl文件
                with open(file_path, 'rb') as f:
                    figure = pickle.load(f)
                
                # 应用样式
                styles_applied = False
                if len(figure.axes) > 0:
                    ax = figure.axes[0]
                    lines = ax.lines
                    
                    for line in lines:
                        # 尝试多种方式获取算法名称
                        algorithm_name = self._getAlgorithmNameForLine(line, ax, lines)
                        
                        if algorithm_name and algorithm_name in self.algorithm_styles:
                            style = self.algorithm_styles[algorithm_name]
                            
                            # 应用样式
                            if 'color' in style:
                                line.set_color(style['color'])
                            if 'linewidth' in style:
                                line.set_linewidth(style['linewidth'])
                            if 'linestyle' in style:
                                line.set_linestyle(style['linestyle'])
                            if 'marker' in style:
                                line.set_marker(style['marker'])
                            if 'markersize' in style:
                                line.set_markersize(style['markersize'])
                            if 'alpha' in style:
                                line.set_alpha(style['alpha'])
                            if 'visible' in style:
                                line.set_visible(style['visible'])  # 批处理时也应用可见性
                            if 'zorder' in style:
                                line.set_zorder(style['zorder'])  # 批处理时也应用图层顺序
                            
                            styles_applied = True
                
                # 如果应用了样式，需要更新图例并重新绘制图表
                if styles_applied:
                    # 更新图例样式以匹配线条样式
                    self._updateLegendStyles(ax)
                    
                    # 创建一个临时的canvas来重新绘制
                    from matplotlib.backends.backend_agg import FigureCanvasAgg
                    canvas = FigureCanvasAgg(figure)
                    canvas.draw()
                
                # 保存PKL文件
                with open(file_path, 'wb') as f:
                    pickle.dump(figure, f)
                
                # 如果需要，导出PNG文件
                if self.export_png:
                    try:
                        png_path = Path(file_path).with_suffix('.png')
                        figure.savefig(png_path, dpi=300, bbox_inches='tight', 
                                     facecolor='white', edgecolor='none')
                        self.status.emit(f"已导出PNG: {png_path.name}")
                    except Exception as png_error:
                        self.status.emit(f"PNG导出失败 {Path(file_path).name}: {str(png_error)}")
                
                # 如果需要，导出PDF文件
                if self.export_pdf:
                    try:
                        pdf_path = Path(file_path).with_suffix('.pdf')
                        figure.savefig(pdf_path, format='pdf', bbox_inches='tight', 
                                     dpi=300, facecolor='white', edgecolor='none')
                        self.status.emit(f"已导出PDF: {pdf_path.name}")
                    except Exception as pdf_error:
                        self.status.emit(f"PDF导出失败 {Path(file_path).name}: {str(pdf_error)}")
                
                success_count += 1
                
            except Exception as e:
                self.status.emit(f"处理文件失败 {Path(file_path).name}: {str(e)}")
            
            # 更新进度
            progress = int((i + 1) / total_count * 100)
            self.progress.emit(progress)
        
        self.finished_signal.emit(success_count, total_count)
    
    def _getAlgorithmNameForLine(self, line, ax, lines):
        """为线条获取有效的算法名称"""
        # 首先尝试从线条标签获取
        label = line.get_label()
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 如果标签无效，尝试从图例获取
        try:
            legend = ax.get_legend()
            if legend:
                # 找到这条线在lines列表中的索引
                line_index = -1
                for i, l in enumerate(lines):
                    if l is line:
                        line_index = i
                        break
                
                # 获取对应的图例文本
                if line_index >= 0 and len(legend.get_texts()) > line_index:
                    legend_text = legend.get_texts()[line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        return legend_text.strip()
        except:
            pass
        
        return None
    
    def _updateLegendStyles(self, ax):
        """更新图例样式以匹配线条样式"""
        try:
            legend = ax.get_legend()
            if legend:
                # 获取图例中的线条对象
                legend_lines = legend.get_lines()
                plot_lines = ax.lines
                
                # 确保图例线条数量与绘图线条数量匹配
                for i, (legend_line, plot_line) in enumerate(zip(legend_lines, plot_lines)):
                    # 同步样式
                    legend_line.set_color(plot_line.get_color())
                    legend_line.set_linewidth(plot_line.get_linewidth())
                    legend_line.set_linestyle(plot_line.get_linestyle())
                    legend_line.set_marker(plot_line.get_marker())
                    legend_line.set_markersize(plot_line.get_markersize())
                    legend_line.set_alpha(plot_line.get_alpha())
        except Exception as e:
            # 如果图例更新失败，不影响主要功能
            pass


class ColorButton(QPushButton):
    """自定义颜色选择按钮"""
    colorChanged = pyqtSignal(str)
    
    def __init__(self, color='blue'):
        super().__init__()
        self.setFixedSize(40, 30)
        self.color = color
        self.updateColor()
        self.clicked.connect(self.chooseColor)
    
    def updateColor(self):
        """更新按钮颜色显示"""
        try:
            # 将matplotlib颜色转换为RGB
            rgb = mcolors.to_rgb(self.color)
            r, g, b = [int(c * 255) for c in rgb]
            self.setStyleSheet(f"background-color: rgb({r}, {g}, {b}); border: 1px solid black;")
        except:
            self.setStyleSheet("background-color: gray; border: 1px solid black;")
    
    def chooseColor(self):
        """打开颜色选择对话框"""
        color_dialog = QColorDialog()
        
        # 设置当前颜色
        try:
            current_qcolor = QColor(self.color)
            color_dialog.setCurrentColor(current_qcolor)
        except:
            pass
        
        # 连接颜色改变信号以实现实时预览
        color_dialog.currentColorChanged.connect(self.onColorPreview)
        
        if color_dialog.exec_():
            qcolor = color_dialog.currentColor()
            # 转换为matplotlib颜色格式
            self.color = f"#{qcolor.red():02x}{qcolor.green():02x}{qcolor.blue():02x}"
            self.updateColor()
            self.colorChanged.emit(self.color)
        else:
            # 如果取消，恢复原来的颜色
            self.colorChanged.emit(self.color)
    
    def onColorPreview(self, qcolor):
        """颜色预览"""
        preview_color = f"#{qcolor.red():02x}{qcolor.green():02x}{qcolor.blue():02x}"
        self.colorChanged.emit(preview_color)
    
    def setColor(self, color):
        """设置颜色"""
        self.color = color
        self.updateColor()


class LinePropertiesWidget(QWidget):
    """线条属性编辑控件"""
    propertiesChanged = pyqtSignal()
    styleChanged = pyqtSignal(str, dict)  # 算法名称, 样式字典
    
    def __init__(self, line, line_index, config_manager=None):
        super().__init__()
        self.line = line
        self.line_index = line_index
        self.config_manager = config_manager
        self.setupUI()
        self.loadLineProperties()
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 算法标识 - 显示算法名称和颜色预览
        legend_name = self.line.get_label()
        
        # 尝试从图例中获取更准确的算法名称
        try:
            if hasattr(self.line, 'axes') and self.line.axes:
                ax = self.line.axes
                legend = ax.get_legend()
                if legend and len(legend.get_texts()) > self.line_index:
                    legend_text = legend.get_texts()[self.line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        legend_name = legend_text.strip()
        except:
            pass  # 如果获取失败，使用原来的名称
        
        # 如果仍然没有有效名称，使用默认名称
        if not legend_name or legend_name.startswith('_') or not legend_name.strip():
            legend_name = f"Line {self.line_index + 1}"
        
        # 创建标题容器，包含颜色预览 - 固定大小
        title_container = QWidget()
        title_container.setFixedHeight(50)  # 固定高度
        title_container.setStyleSheet("""
            QWidget {
                background-color: #e8f4fd;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin: 2px;
                padding: 8px;
            }
        """)
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(5, 5, 5, 5)
        
        # 颜色预览方块
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(24, 24)
        self.color_preview.setStyleSheet("QLabel { border: 2px solid black; border-radius: 4px; }")
        self.updateColorPreview()
        
        
        # 算法名称标签 - 支持文本省略
        self.label = QLabel(f"算法: {legend_name}")
        self.label.setFont(QFont("Arial", 11, QFont.Bold))
        self.label.setStyleSheet("QLabel { color: #2c3e50; background-color: transparent; }")
        # 设置文本省略模式，当文本过长时显示省略号
        self.label.setWordWrap(False)
        self.label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        # 设置工具提示显示完整名称
        self.label.setToolTip(f"算法: {legend_name}")
        
        # 算法信息按钮
        self.info_btn = QPushButton("ℹ")
        self.info_btn.setFixedSize(24, 24)
        self.info_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.info_btn.clicked.connect(self.showAlgorithmInfo)
        
        title_layout.addWidget(self.color_preview)
        title_layout.addWidget(self.label)
        title_layout.addStretch()
        title_layout.addWidget(self.info_btn)
        title_container.setLayout(title_layout)
        layout.addWidget(title_container)
        
        # 创建属性编辑区域 - 使用更紧凑的布局
        props_layout = QGridLayout()
        
        # 第一行：颜色和透明度
        props_layout.addWidget(QLabel("颜色:"), 0, 0)
        self.color_button = ColorButton()
        self.color_button.colorChanged.connect(self.onColorChanged)
        props_layout.addWidget(self.color_button, 0, 1)
        
        props_layout.addWidget(QLabel("透明度:"), 0, 2)
        alpha_container = QWidget()
        alpha_layout = QHBoxLayout()
        alpha_layout.setContentsMargins(0, 0, 0, 0)
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setRange(0, 100)
        self.alpha_slider.setValue(100)
        self.alpha_slider.valueChanged.connect(self.onAlphaChanged)
        self.alpha_label = QLabel("1.0")
        self.alpha_label.setMinimumWidth(30)
        alpha_layout.addWidget(self.alpha_slider)
        alpha_layout.addWidget(self.alpha_label)
        alpha_container.setLayout(alpha_layout)
        props_layout.addWidget(alpha_container, 0, 3, 1, 2)
        
        # 第二行：线宽和线型
        props_layout.addWidget(QLabel("线宽:"), 1, 0)
        self.linewidth_spin = QDoubleSpinBox()
        self.linewidth_spin.setRange(0.1, 10.0)
        self.linewidth_spin.setSingleStep(0.1)
        self.linewidth_spin.setValue(1.0)
        self.linewidth_spin.setMaximumWidth(80)
        self.linewidth_spin.valueChanged.connect(self.onLinewidthChanged)
        props_layout.addWidget(self.linewidth_spin, 1, 1)
        
        props_layout.addWidget(QLabel("线型:"), 1, 2)
        self.linestyle_combo = QComboBox()
        self.linestyle_combo.addItems(['-', '--', '-.', ':', 'None'])
        self.linestyle_combo.currentTextChanged.connect(self.onLinestyleChanged)
        props_layout.addWidget(self.linestyle_combo, 1, 3)
        
        # 第三行：标记类型和大小
        props_layout.addWidget(QLabel("标记:"), 2, 0)
        self.marker_combo = QComboBox()
        markers = ['None', 'o', 's', '^', 'v', '<', '>', 'D', 'p', '*', 'h', 'H', '+', 'x', '|', '_']
        self.marker_combo.addItems(markers)
        self.marker_combo.currentTextChanged.connect(self.onMarkerChanged)
        props_layout.addWidget(self.marker_combo, 2, 1)
        
        props_layout.addWidget(QLabel("标记大小:"), 2, 2)
        self.markersize_spin = QDoubleSpinBox()
        self.markersize_spin.setRange(1.0, 20.0)
        self.markersize_spin.setValue(6.0)
        self.markersize_spin.setMaximumWidth(80)
        self.markersize_spin.valueChanged.connect(self.onMarkersizeChanged)
        props_layout.addWidget(self.markersize_spin, 2, 3)
        
        # 第四行：可见性和保存按钮
        self.visible_check = QCheckBox("显示")
        self.visible_check.setChecked(True)
        self.visible_check.toggled.connect(self.onVisibilityChanged)
        props_layout.addWidget(self.visible_check, 3, 0)
        
        self.save_style_btn = QPushButton("保存样式")
        self.save_style_btn.clicked.connect(self.saveCurrentStyle)
        self.save_style_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        props_layout.addWidget(self.save_style_btn, 3, 1, 1, 2)
        
        layout.addLayout(props_layout)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("QFrame { color: #cccccc; }")
        layout.addWidget(line)
        
        # 设置整体样式
        self.setLayout(layout)
        self.setStyleSheet("""
            LinePropertiesWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 3px;
                padding: 8px;
            }
            QLabel {
                background-color: transparent;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
            QComboBox, QDoubleSpinBox {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
            QComboBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
            QCheckBox {
                background-color: transparent;
                font-weight: bold;
                color: #2c3e50;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 6px;
                background: #ecf0f1;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #2980b9;
            }
        """)
    
    def loadLineProperties(self):
        """从线条对象加载属性"""
        # 颜色
        color = self.line.get_color()
        self.color_button.setColor(color)
        self.updateColorPreview()
        
        # 透明度
        alpha = self.line.get_alpha()
        if alpha is None:
            alpha = 1.0
        self.alpha_slider.setValue(int(alpha * 100))
        self.alpha_label.setText(f"{alpha:.2f}")
        
        # 线宽
        linewidth = self.line.get_linewidth()
        self.linewidth_spin.setValue(linewidth)
        
        # 线型
        linestyle = self.line.get_linestyle()
        # 修复findText的参数类型问题
        linestyle_str = str(linestyle) if linestyle is not None else '-'
        index = self.linestyle_combo.findText(linestyle_str)
        if index >= 0:
            self.linestyle_combo.setCurrentIndex(index)
        
        # 标记
        marker = self.line.get_marker()
        if marker == 'None' or marker is None:
            marker = 'None'
        # 修复findText的参数类型问题
        marker_str = str(marker) if marker is not None else 'None'
        index = self.marker_combo.findText(marker_str)
        if index >= 0:
            self.marker_combo.setCurrentIndex(index)
        
        # 标记大小
        markersize = self.line.get_markersize()
        self.markersize_spin.setValue(markersize)
        
        # 可见性
        visible = self.line.get_visible()
        self.visible_check.setChecked(visible)
        
        # 自动应用保存的样式（如果存在）
        if self.config_manager and not hasattr(self, '_loading_saved_style'):
            self._loading_saved_style = True  # 防止递归调用
            algorithm_name = self._getEffectiveAlgorithmName(self.line.get_label())
            if algorithm_name:
                saved_style = self.config_manager.get_algorithm_style(algorithm_name)
                if saved_style:
                    # 应用保存的样式到线条
                    if 'color' in saved_style:
                        self.line.set_color(saved_style['color'])
                    if 'linewidth' in saved_style:
                        self.line.set_linewidth(saved_style['linewidth'])
                    if 'linestyle' in saved_style:
                        self.line.set_linestyle(saved_style['linestyle'])
                    if 'marker' in saved_style:
                        self.line.set_marker(saved_style['marker'])
                    if 'markersize' in saved_style:
                        self.line.set_markersize(saved_style['markersize'])
                    if 'alpha' in saved_style:
                        self.line.set_alpha(saved_style['alpha'])
                    if 'visible' in saved_style:
                        self.line.set_visible(saved_style['visible'])  # 应用保存的可见性
                    if 'zorder' in saved_style:
                        self.line.set_zorder(saved_style['zorder'])  # 应用保存的图层顺序
                    
                    # 重新读取线条属性到控件（不递归调用保存样式部分）
                    color = self.line.get_color()
                    self.color_button.setColor(color)
                    self.updateColorPreview()
                    
                    alpha = self.line.get_alpha()
                    if alpha is None:
                        alpha = 1.0
                    self.alpha_slider.setValue(int(alpha * 100))
                    self.alpha_label.setText(f"{alpha:.2f}")
                    
                    self.linewidth_spin.setValue(self.line.get_linewidth())
                    
                    linestyle = str(self.line.get_linestyle())
                    index = self.linestyle_combo.findText(linestyle)
                    if index >= 0:
                        self.linestyle_combo.setCurrentIndex(index)
                    
                    marker = str(self.line.get_marker())
                    if marker == 'None' or marker is None:
                        marker = 'None'
                    index = self.marker_combo.findText(marker)
                    if index >= 0:
                        self.marker_combo.setCurrentIndex(index)
                    
                    self.markersize_spin.setValue(self.line.get_markersize())
                    self.visible_check.setChecked(self.line.get_visible())
            
            del self._loading_saved_style  # 清除标记
    
    def updateColorPreview(self):
        """更新颜色预览"""
        try:
            color = self.line.get_color()
            rgb = mcolors.to_rgb(color)
            r, g, b = [int(c * 255) for c in rgb]
            self.color_preview.setStyleSheet(
                f"QLabel {{ background-color: rgb({r}, {g}, {b}); border: 1px solid black; }}")
        except:
            self.color_preview.setStyleSheet("QLabel { background-color: gray; border: 1px solid black; }")
    
    def onColorChanged(self, color):
        """颜色改变"""
        self.line.set_color(color)
        self.updateColorPreview()
        self.propertiesChanged.emit()
        # 立即更新图表显示
        if hasattr(self.line, 'axes') and self.line.axes and hasattr(self.line.axes, 'figure'):
            self.line.axes.figure.canvas.draw_idle()
    
    def onAlphaChanged(self, value):
        """透明度改变"""
        alpha = value / 100.0
        self.line.set_alpha(alpha)
        self.alpha_label.setText(f"{alpha:.2f}")
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onLinewidthChanged(self, value):
        """线宽改变"""
        self.line.set_linewidth(value)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onLinestyleChanged(self, style):
        """线型改变"""
        self.line.set_linestyle(style)
        self.propertiesChanged.emit()
        # 立即更新图表显示
    
        self._updateCanvas()
    
    def onMarkerChanged(self, marker):
        """标记改变"""
        if marker == 'None':
            marker = 'None'
        self.line.set_marker(marker)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onMarkersizeChanged(self, size):
        """标记大小改变"""
        self.line.set_markersize(size)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onVisibilityChanged(self, visible):
        """可见性改变"""
        self.line.set_visible(visible)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def _updateCanvas(self):
        """更新画布显示"""
        if hasattr(self.line, 'axes') and self.line.axes and hasattr(self.line.axes, 'figure'):
            self.line.axes.figure.canvas.draw_idle()
    
    def getCurrentStyle(self):
        """获取当前样式"""
        return {
            'color': self.line.get_color(),
            'linewidth': self.line.get_linewidth(),
            'linestyle': self.line.get_linestyle(),
            'marker': self.line.get_marker(),
            'markersize': self.line.get_markersize(),
            'alpha': self.line.get_alpha() if self.line.get_alpha() is not None else 1.0,
            'visible': self.line.get_visible(),
            'zorder': self.line.get_zorder()  # 添加图层顺序保存
        }
    
    def saveCurrentStyle(self):
        """保存当前样式"""
        algorithm_name = self.line.get_label()
        
        # 获取有效的算法名称
        effective_name = self._getEffectiveAlgorithmName(algorithm_name)
        
        if effective_name:
            style = self.getCurrentStyle()
            if self.config_manager:
                self.config_manager.save_algorithm_style(effective_name, style)
            self.styleChanged.emit(effective_name, style)
            QMessageBox.information(self, "成功", f"已保存算法 '{effective_name}' 的样式")
        else:
            # 如果没有有效名称，提供输入对话框让用户自定义
            custom_name, ok = QInputDialog.getText(
                self, 
                "输入算法名称", 
                "请为此算法输入一个名称:",
                text=f"Algorithm_{self.line_index + 1}"
            )
            
            if ok and custom_name.strip():
                effective_name = custom_name.strip()
                style = self.getCurrentStyle()
                if self.config_manager:
                    self.config_manager.save_algorithm_style(effective_name, style)
                self.styleChanged.emit(effective_name, style)
                QMessageBox.information(self, "成功", f"已保存算法 '{effective_name}' 的样式")
            else:
                QMessageBox.warning(self, "警告", "保存取消：未提供有效的算法名称")
    
    def _getEffectiveAlgorithmName(self, label):
        """获取有效的算法名称"""
        # 首先尝试从图例获取名称
        try:
            if hasattr(self.line, 'axes') and self.line.axes:
                ax = self.line.axes
                legend = ax.get_legend()
                if legend and len(legend.get_texts()) > self.line_index:
                    legend_text = legend.get_texts()[self.line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        return legend_text.strip()
        except:
            pass
        
        # 如果图例名称无效，检查线条标签
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 都无效则返回None
        return None
    
    def showAlgorithmInfo(self):
        """显示算法信息"""
        algorithm_name = self._getEffectiveAlgorithmName(self.line.get_label())
        if not algorithm_name:
            algorithm_name = f"Line {self.line_index + 1}"
        
        # 获取线条的详细信息
        info_text = f"""
算法名称: {algorithm_name}

当前属性:
• 颜色: {self.line.get_color()}
• 线宽: {self.line.get_linewidth()}
• 线型: {self.line.get_linestyle()}
• 标记: {self.line.get_marker()}
• 标记大小: {self.line.get_markersize()}
• 透明度: {self.line.get_alpha() if self.line.get_alpha() is not None else 1.0}
• 可见性: {'是' if self.line.get_visible() else '否'}

数据点数量: {len(self.line.get_xdata())}
X范围: {min(self.line.get_xdata()):.3f} - {max(self.line.get_xdata()):.3f}
Y范围: {min(self.line.get_ydata()):.3f} - {max(self.line.get_ydata()):.3f}
        """
        
        QMessageBox.information(self, f"算法信息 - {algorithm_name}", info_text.strip())


class FileTabWidget(QWidget):
    """文件标签页管理组件"""
    fileSelected = pyqtSignal(str)  # 选中文件时触发
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_gui = parent
        self.setupUI()
        
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        self.setLayout(layout)
        
        # 标题
        title_label = QLabel("已打开的文件")
        title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #333333;
                padding: 5px;
                background-color: #e8f4f8;
                border-radius: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
                font-size: 11px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eeeeee;
            }
            QListWidget::item:selected {
                background-color: #d4edda;
                color: #155724;
                font-weight: bold;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
        """)
        self.file_list.itemClicked.connect(self.onFileSelected)
        layout.addWidget(self.file_list)
        
        # 文件操作按钮
        button_layout = QVBoxLayout()
        button_layout.setSpacing(3)
        
        self.close_btn = QPushButton("关闭文件")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.close_btn.clicked.connect(self.closeCurrentFile)
        self.close_btn.setEnabled(False)
        button_layout.addWidget(self.close_btn)
        
        self.close_all_btn = QPushButton("关闭全部")
        self.close_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.close_all_btn.clicked.connect(self.closeAllFiles)
        self.close_all_btn.setEnabled(False)
        button_layout.addWidget(self.close_all_btn)
        
        layout.addLayout(button_layout)
        
        # 弹簧，使内容顶部对齐
        layout.addStretch()
        
    def addFile(self, file_path, display_name=None):
        """添加文件到列表"""
        if display_name is None:
            display_name = Path(file_path).name
        
        # 检查是否已存在
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                # 已存在，选中它
                self.file_list.setCurrentItem(item)
                return
        
        # 添加新项
        item = QListWidgetItem(display_name)
        item.setData(Qt.UserRole, file_path)
        item.setToolTip(file_path)
        self.file_list.addItem(item)
        
        # 选中新添加的项
        self.file_list.setCurrentItem(item)
        
        # 更新按钮状态
        self.updateButtonState()
        
    def removeFile(self, file_path):
        """从列表移除文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                self.file_list.takeItem(i)
                break
        
        self.updateButtonState()
        
    def getCurrentFile(self):
        """获取当前选中的文件"""
        current = self.file_list.currentItem()
        if current:
            return current.data(Qt.UserRole)
        return None
        
    def setCurrentFile(self, file_path):
        """设置当前选中的文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                self.file_list.setCurrentItem(item)
                break
                
    def getFileCount(self):
        """获取文件数量"""
        return self.file_list.count()
        
    def getAllFiles(self):
        """获取所有文件路径"""
        files = []
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            files.append(item.data(Qt.UserRole))
        return files
        
    def onFileSelected(self, item):
        """文件被选中时"""
        file_path = item.data(Qt.UserRole)
        self.fileSelected.emit(file_path)
        self.updateButtonState()
        
    def closeCurrentFile(self):
        """关闭当前文件"""
        current = self.file_list.currentItem()
        if current and self.parent_gui:
            file_path = current.data(Qt.UserRole)
            self.parent_gui.closeFile(file_path)
            
    def closeAllFiles(self):
        """关闭所有文件"""
        if self.parent_gui:
            self.parent_gui.closeAllFiles()
            
    def updateButtonState(self):
        """更新按钮状态"""
        has_files = self.file_list.count() > 0
        has_selection = self.file_list.currentItem() is not None
        
        self.close_btn.setEnabled(has_selection)
        self.close_all_btn.setEnabled(has_files)


class PlotEditorGUI(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.figure = None
        self.canvas = None
        self.current_file = None
        self.line_widgets = []
        self.config_manager = ConfigManager()
        
        # 多文件管理
        self.opened_files = {}  # 文件路径 -> 文件状态字典
        self.temp_save_dir = None  # 临时文件目录
        
        # 创建临时目录
        self.createTempDirectory()
        
        self.setupUI()
        self.setWindowTitle("matplotlib图表编辑器 v1.1.0")
        self.resize(1600, 900)  # 增加默认宽度以适应侧边栏
        
        # 设置主窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #e7e7e7;
                border: 1px solid #adadad;
                border-radius: 3px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d4edda;
            }
            QPushButton:pressed {
                background-color: #c3e6cb;
            }
            QPushButton:disabled {
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)

    def createTempDirectory(self):
        """创建临时文件目录"""
        try:
            self.temp_save_dir = tempfile.mkdtemp(prefix="plot_editor_temp_")
            print(f"创建临时目录: {self.temp_save_dir}")
        except Exception as e:
            print(f"创建临时目录失败: {e}")
            self.temp_save_dir = None

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理临时文件
        self.cleanupTempDirectory()
        event.accept()

    def cleanupTempDirectory(self):
        """清理临时文件目录"""
        if self.temp_save_dir and Path(self.temp_save_dir).exists():
            try:
                shutil.rmtree(self.temp_save_dir)
                print(f"清理临时目录: {self.temp_save_dir}")
            except Exception as e:
                print(f"清理临时目录失败: {e}")

    def setupUI(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        central_widget.setLayout(main_layout)
        
        # 创建顶部工具栏（包含侧边栏切换按钮）
        toolbar_layout = self.createTopToolbar()
        main_layout.addLayout(toolbar_layout)
        
        # 创建主分割器 - 水平分割（侧边栏 | 内容区域）
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setHandleWidth(8)
        self.main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                border: 1px solid #999999;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bbbbbb;
            }
            QSplitter::handle:pressed {
                background-color: #aaaaaa;
            }
        """)
        main_layout.addWidget(self.main_splitter)
        
        # 左侧：文件侧边栏
        self.file_sidebar = self.createFileSidebar()
        self.main_splitter.addWidget(self.file_sidebar)
        
        # 中间内容区域：创建内容分割器（算法区域 | 图表和控制区域）
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setHandleWidth(8)
        content_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                border: 1px solid #999999;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bbbbbb;
            }
            QSplitter::handle:pressed {
                background-color: #aaaaaa;
            }
        """)
        self.main_splitter.addWidget(content_splitter)
        
        # 算法选择和属性区域
        algorithm_panel = self.createAlgorithmPanel()
        content_splitter.addWidget(algorithm_panel)
        
        # 右侧：创建垂直分割器（图表区域 | 控制区域）
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setHandleWidth(8)
        right_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                border: 1px solid #999999;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bbbbbb;
            }
            QSplitter::handle:pressed {
                background-color: #aaaaaa;
            }
        """)
        content_splitter.addWidget(right_splitter)
        
        # 右上：图表显示区域
        plot_area = self.createPlotArea()
        right_splitter.addWidget(plot_area)
        
        # 右下：控制面板区域
        control_panel = self.createControlPanel()
        right_splitter.addWidget(control_panel)
        
        # 设置分割器比例
        self.main_splitter.setSizes([200, 1400])  # 侧边栏 | 内容区域
        content_splitter.setSizes([350, 1050])  # 算法区域 | 图表和控制区域
        right_splitter.setSizes([700, 300])  # 图表区域 | 控制区域
        
        # 创建菜单栏
        self.createMenuBar()

    def createTopToolbar(self):
        """创建顶部工具栏"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 5)
        
        # 侧边栏显示/隐藏按钮
        self.sidebar_toggle_btn = QPushButton()
        self.sidebar_toggle_btn.setFixedSize(32, 32)
        self.sidebar_toggle_btn.setToolTip("显示/隐藏文件列表")
        self.sidebar_toggle_btn.clicked.connect(self.toggleSidebar)
        
        # 设置图标样式 - 显示状态（列表图标）
        self.updateSidebarToggleButton(True)
        
        self.sidebar_toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 16px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        
        layout.addWidget(self.sidebar_toggle_btn)
        
        # 添加弹簧，将按钮推到右侧
        layout.addStretch()
        
        return layout

    def updateSidebarToggleButton(self, sidebar_visible):
        """更新侧边栏切换按钮的图标"""
        if sidebar_visible:
            # 显示状态 - 使用列表图标
            self.sidebar_toggle_btn.setText("☰")
            self.sidebar_toggle_btn.setToolTip("隐藏文件列表")
        else:
            # 隐藏状态 - 使用展开图标
            self.sidebar_toggle_btn.setText("▶")
            self.sidebar_toggle_btn.setToolTip("显示文件列表")

    def createFileSidebar(self):
        """创建文件侧边栏"""
        sidebar = QWidget()
        sidebar.setMinimumWidth(180)
        sidebar.setMaximumWidth(300)
        sidebar.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        sidebar.setLayout(layout)
        
        # 创建文件标签页组件
        self.file_tab_widget = FileTabWidget(self)
        self.file_tab_widget.fileSelected.connect(self.switchToFile)
        layout.addWidget(self.file_tab_widget)
        
        return sidebar

    def toggleSidebar(self):
        """切换侧边栏显示/隐藏"""
        sidebar_visible = self.file_sidebar.isVisible()
        
        if sidebar_visible:
            # 隐藏侧边栏
            self.file_sidebar.hide()
            self.updateSidebarToggleButton(False)
        else:
            # 显示侧边栏
            self.file_sidebar.show()
            self.updateSidebarToggleButton(True)

    def createMenuBar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        open_action = file_menu.addAction('打开 .pkl 文件')
        open_action.triggered.connect(self.openFile)
        
        save_action = file_menu.addAction('保存')
        save_action.triggered.connect(self.saveFile)
        
        save_as_action = file_menu.addAction('另存为')
        save_as_action.triggered.connect(self.saveAsFile)
        
        file_menu.addSeparator()
        
        export_png_action = file_menu.addAction('导出为PNG')
        export_png_action.triggered.connect(self.exportPNG)
        
        export_pdf_action = file_menu.addAction('导出为PDF')
        export_pdf_action.triggered.connect(self.exportPDF)
        
        export_svg_action = file_menu.addAction('导出为SVG')
        export_svg_action.triggered.connect(self.exportSVG)
        
        file_menu.addSeparator()
        
        batch_action = file_menu.addAction('批处理')
        batch_action.triggered.connect(self.batchProcess)
        
        # 样式菜单
        style_menu = menubar.addMenu('样式')
        
        save_style_action = style_menu.addAction('保存工程样式')
        save_style_action.triggered.connect(self.saveProjectStyle)
        
        load_style_action = style_menu.addAction('打开工程样式')
        load_style_action.triggered.connect(self.loadProjectStyle)
        
        style_menu.addSeparator()
        
        layer_order_action = style_menu.addAction('调整图层顺序')
        layer_order_action.triggered.connect(self.adjustLayerOrder)
        
        # 窗口菜单
        window_menu = menubar.addMenu('窗口')
        
        toggle_sidebar_action = window_menu.addAction('切换文件列表')
        toggle_sidebar_action.triggered.connect(self.toggleSidebar)
        toggle_sidebar_action.setShortcut('Ctrl+B')
        
        window_menu.addSeparator()
        
        close_current_action = window_menu.addAction('关闭当前文件')
        close_current_action.triggered.connect(self.closeCurrentFile)
        close_current_action.setShortcut('Ctrl+W')
        
        close_all_action = window_menu.addAction('关闭所有文件')
        close_all_action.triggered.connect(self.closeAllFiles)
        close_all_action.setShortcut('Ctrl+Shift+W')
    
    def createAlgorithmPanel(self):
        """创建算法选择和属性面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        panel.setLayout(layout)
        
        # 算法选择区域
        algorithm_group = QGroupBox("算法选择")
        algorithm_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        algorithm_layout = QVBoxLayout()
        
        # 算法选择按钮
        self.algorithm_menu_btn = QPushButton("选择算法")
        self.algorithm_menu_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # 创建算法选择菜单
        self.algorithm_menu = QMenu(self)
        self.algorithm_menu_btn.setMenu(self.algorithm_menu)
        
        # 当前选择的算法显示
        self.current_algorithm = "all"
        self.selected_algorithm_label = QLabel("当前: 所有算法")
        self.selected_algorithm_label.setStyleSheet("""
            QLabel { 
                color: #2c3e50; 
                font-weight: bold; 
                font-size: 12px;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
                border: 1px solid #bdc3c7;
            }
        """)
        
        algorithm_layout.addWidget(self.algorithm_menu_btn)
        algorithm_layout.addWidget(self.selected_algorithm_label)
        algorithm_group.setLayout(algorithm_layout)
        layout.addWidget(algorithm_group)
        
        # 算法属性区域
        properties_group = QGroupBox("算法属性")
        properties_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        properties_layout = QVBoxLayout()
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(200)  # 设置最小高度，确保有足够空间显示算法属性
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
        
        scroll_widget = QWidget()
        scroll_widget.setStyleSheet("QWidget { background-color: transparent; }")
        self.properties_layout = QVBoxLayout()
        self.properties_layout.setSpacing(8)
        scroll_widget.setLayout(self.properties_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        properties_layout.addWidget(scroll_area)
        properties_group.setLayout(properties_layout)
        layout.addWidget(properties_group)
        
        return panel
    
    def createControlPanel(self):
        """创建控制面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:horizontal, QScrollBar:vertical {
                background-color: #ecf0f1;
                border-radius: 6px;
            }
            QScrollBar:horizontal {
                height: 12px;
            }
            QScrollBar:vertical {
                width: 12px;
            }
            QScrollBar::handle:horizontal, QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover, QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
        
        scroll_widget = QWidget()
        layout = QHBoxLayout()  # 使用水平布局来容纳多个区域
        layout.setContentsMargins(10, 10, 10, 10)
        scroll_widget.setLayout(layout)
        
        # 文件操作区域
        file_group = QGroupBox("文件操作")
        file_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        file_layout = QVBoxLayout()
        
        self.open_button = QPushButton("打开 .pkl 文件")
        self.open_button.clicked.connect(self.openFile)
        file_layout.addWidget(self.open_button)
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.saveFile)
        self.save_button.setEnabled(False)
        file_layout.addWidget(self.save_button)
        
        # 导出按钮（下拉菜单）
        self.export_button = QPushButton("导出图片")
        self.export_button.setEnabled(False)
        
        # 创建导出菜单
        export_menu = QMenu(self)
        export_png_action = export_menu.addAction("导出为PNG")
        export_png_action.triggered.connect(self.exportPNG)
        export_pdf_action = export_menu.addAction("导出为PDF")
        export_pdf_action.triggered.connect(self.exportPDF)
        export_svg_action = export_menu.addAction("导出为SVG")
        export_svg_action.triggered.connect(self.exportSVG)
        
        self.export_button.setMenu(export_menu)
        file_layout.addWidget(self.export_button)
        
        self.batch_button = QPushButton("批处理")
        self.batch_button.clicked.connect(self.batchProcess)
        file_layout.addWidget(self.batch_button)
        
        # 保存样式按钮
        self.save_styles_button = QPushButton("保存样式")
        self.save_styles_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                color: #7f8c8d;
            }
        """)
        self.save_styles_button.clicked.connect(self.saveAllAlgorithmStyles)
        self.save_styles_button.setEnabled(False)
        self.save_styles_button.setToolTip("保存当前所有算法的样式设置")
        file_layout.addWidget(self.save_styles_button)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 图表属性区域
        chart_group = QGroupBox("图表属性")
        chart_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        chart_layout = QGridLayout()
        
        # 标题编辑
        chart_layout.addWidget(QLabel("标题:"), 0, 0)
        self.title_edit = QLineEdit()
        self.title_edit.textChanged.connect(self.onTitleChanged)
        chart_layout.addWidget(self.title_edit, 0, 1)
        
        # X轴标签
        chart_layout.addWidget(QLabel("X轴标签:"), 1, 0)
        self.xlabel_edit = QLineEdit()
        self.xlabel_edit.textChanged.connect(self.onXLabelChanged)
        chart_layout.addWidget(self.xlabel_edit, 1, 1)
        
        # Y轴标签
        chart_layout.addWidget(QLabel("Y轴标签:"), 2, 0)
        self.ylabel_edit = QLineEdit()
        self.ylabel_edit.textChanged.connect(self.onYLabelChanged)
        chart_layout.addWidget(self.ylabel_edit, 2, 1)
        
        # 网格显示
        self.grid_check = QCheckBox("显示网格")
        self.grid_check.toggled.connect(self.onGridToggled)
        chart_layout.addWidget(self.grid_check, 3, 0, 1, 2)
        
        # 统一规范化按钮
        self.normalize_all_btn = QPushButton("规范化标题和标签")
        self.normalize_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.normalize_all_btn.clicked.connect(self.normalizeAllLabels)
        chart_layout.addWidget(self.normalize_all_btn, 4, 0, 1, 2)
        
        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)
        
        # 坐标轴设置区域
        axis_group = QGroupBox("坐标轴设置")
        axis_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        axis_layout = QGridLayout()
        
        # X轴刻度类型
        axis_layout.addWidget(QLabel("X轴刻度:"), 0, 0)
        self.xscale_combo = QComboBox()
        self.xscale_combo.addItems(["linear", "log"])
        self.xscale_combo.currentTextChanged.connect(self.onXScaleChanged)
        axis_layout.addWidget(self.xscale_combo, 0, 1)
        
        # Y轴刻度类型
        axis_layout.addWidget(QLabel("Y轴刻度:"), 1, 0)
        self.yscale_combo = QComboBox()
        self.yscale_combo.addItems(["linear", "log"])
        self.yscale_combo.currentTextChanged.connect(self.onYScaleChanged)
        axis_layout.addWidget(self.yscale_combo, 1, 1)
        
        # X轴范围
        axis_layout.addWidget(QLabel("X轴范围:"), 2, 0)
        x_range_container = QWidget()
        x_range_layout = QHBoxLayout()
        x_range_layout.setContentsMargins(0, 0, 0, 0)
        self.xmin_edit = QLineEdit()
        self.xmin_edit.setPlaceholderText("最小值")
        self.xmin_edit.textChanged.connect(self.onXRangeChanged)
        self.xmax_edit = QLineEdit()
        self.xmax_edit.setPlaceholderText("最大值")
        self.xmax_edit.textChanged.connect(self.onXRangeChanged)
        x_range_layout.addWidget(self.xmin_edit)
        x_range_layout.addWidget(QLabel("-"))
        x_range_layout.addWidget(self.xmax_edit)
        x_range_container.setLayout(x_range_layout)
        axis_layout.addWidget(x_range_container, 2, 1)
        
        # Y轴范围
        axis_layout.addWidget(QLabel("Y轴范围:"), 3, 0)
        y_range_container = QWidget()
        y_range_layout = QHBoxLayout()
        y_range_layout.setContentsMargins(0, 0, 0, 0)
        self.ymin_edit = QLineEdit()
        self.ymin_edit.setPlaceholderText("最小值")
        self.ymin_edit.textChanged.connect(self.onYRangeChanged)
        self.ymax_edit = QLineEdit()
        self.ymax_edit.setPlaceholderText("最大值")
        self.ymax_edit.textChanged.connect(self.onYRangeChanged)
        y_range_layout.addWidget(self.ymin_edit)
        y_range_layout.addWidget(QLabel("-"))
        y_range_layout.addWidget(self.ymax_edit)
        y_range_container.setLayout(y_range_layout)
        axis_layout.addWidget(y_range_container, 3, 1)
        
        # 自动范围按钮
        self.auto_range_btn = QPushButton("自动范围")
        self.auto_range_btn.clicked.connect(self.onAutoRange)
        axis_layout.addWidget(self.auto_range_btn, 4, 0, 1, 2)
        
        # 坐标轴刻度字体大小
        axis_layout.addWidget(QLabel("刻度字体:"), 5, 0)
        self.tick_fontsize_spin = QDoubleSpinBox()
        self.tick_fontsize_spin.setRange(6, 20)
        self.tick_fontsize_spin.setValue(10)
        self.tick_fontsize_spin.setSuffix(" pt")
        self.tick_fontsize_spin.valueChanged.connect(self.onTickFontsizeChanged)
        axis_layout.addWidget(self.tick_fontsize_spin, 5, 1)
        
        # 坐标轴标签字体大小
        axis_layout.addWidget(QLabel("标签字体:"), 6, 0)
        self.label_fontsize_spin = QDoubleSpinBox()
        self.label_fontsize_spin.setRange(6, 20)
        self.label_fontsize_spin.setValue(12)
        self.label_fontsize_spin.setSuffix(" pt")
        self.label_fontsize_spin.valueChanged.connect(self.onLabelFontsizeChanged)
        axis_layout.addWidget(self.label_fontsize_spin, 6, 1)
        
        axis_group.setLayout(axis_layout)
        layout.addWidget(axis_group)
        
        # 图例控制区域
        legend_group = QGroupBox("图例设置")
        legend_layout = QGridLayout()
        
        # 图例显示开关
        self.legend_check = QCheckBox("显示图例")
        self.legend_check.toggled.connect(self.onLegendToggled)
        legend_layout.addWidget(self.legend_check, 0, 0, 1, 2)
        
        # 图例位置
        legend_layout.addWidget(QLabel("图例位置:"), 1, 0)
        self.legend_loc_combo = QComboBox()
        self.legend_loc_combo.addItems([
            "best", "upper right", "upper left", "lower left", "lower right",
            "right", "center left", "center right", "lower center", "upper center", "center",
            "outside right", "outside left", "outside top", "outside bottom"
        ])
        self.legend_loc_combo.currentTextChanged.connect(self.onLegendLocationChanged)
        legend_layout.addWidget(self.legend_loc_combo, 1, 1)
        
        # 图例字体大小
        legend_layout.addWidget(QLabel("字体大小:"), 2, 0)
        self.legend_fontsize_spin = QDoubleSpinBox()
        self.legend_fontsize_spin.setRange(6, 20)
        self.legend_fontsize_spin.setValue(10)
        self.legend_fontsize_spin.setSuffix(" pt")
        self.legend_fontsize_spin.valueChanged.connect(self.onLegendFontsizeChanged)
        legend_layout.addWidget(self.legend_fontsize_spin, 2, 1)
        
        # 图例透明度
        legend_layout.addWidget(QLabel("透明度:"), 3, 0)
        self.legend_alpha_slider = QSlider(Qt.Horizontal)
        self.legend_alpha_slider.setRange(0, 100)
        self.legend_alpha_slider.setValue(80)
        self.legend_alpha_slider.valueChanged.connect(self.onLegendAlphaChanged)
        legend_layout.addWidget(self.legend_alpha_slider, 3, 1)
        
        # 编辑图例名称按钮
        self.edit_legend_btn = QPushButton("编辑图例名称")
        self.edit_legend_btn.clicked.connect(self.editLegendNames)
        legend_layout.addWidget(self.edit_legend_btn, 4, 0, 1, 2)
        
        # 调整图层顺序按钮
        self.layer_order_btn = QPushButton("调整图层顺序")
        self.layer_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:pressed {
                background-color: #7d3c98;
            }
        """)
        self.layer_order_btn.clicked.connect(self.adjustLayerOrder)
        legend_layout.addWidget(self.layer_order_btn, 5, 0, 1, 2)
        
        # 新增更新图例按钮
        self.update_legend_btn = QPushButton("更新图例")
        self.update_legend_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3e8e41;
            }
        """)
        self.update_legend_btn.clicked.connect(self.updateLegend)
        legend_layout.addWidget(self.update_legend_btn, 6, 0, 1, 2)
        
        legend_group.setLayout(legend_layout)
        layout.addWidget(legend_group)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        panel_layout = QVBoxLayout()
        panel_layout.setContentsMargins(0, 0, 0, 0)
        panel.setLayout(panel_layout)
        panel_layout.addWidget(scroll_area)
        
        return panel
    
    def createPlotArea(self):
        """创建图表显示区域"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
        """)
        
        # 设置大小策略，确保能够扩展
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(25, 15, 15, 15)  # 左侧增加更多边距确保Y轴标签可见
        widget.setLayout(layout)
        
        # 图表画布将在加载文件时创建
        self.plot_layout = layout
        
        # 设置最小大小
        widget.setMinimumSize(400, 300)
        
        return widget
    
    def openFile(self):
        """打开.pkl文件"""
        last_dir = self.config_manager.get_last_directory()
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开matplotlib图表文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
        
        if file_path:
            # 检查文件是否已经打开
            if file_path in [item.data(Qt.UserRole) for item in [self.file_tab_widget.file_list.item(i) for i in range(self.file_tab_widget.file_list.count())]]:
                # 文件已打开，直接切换
                self.switchToFile(file_path)
                return
                
            try:
                # 保存当前文件状态（如果有的话）
                if self.current_file:
                    self.saveCurrentFileState()
                
                print(f"🔍 加载新文件: {file_path}")
                
                with open(file_path, 'rb') as f:
                    self.figure = pickle.load(f)
                
                print(f"📊 文件加载完成，图表对象: {type(self.figure)}")
                if self.figure and len(self.figure.axes) > 0:
                    ax = self.figure.axes[0]
                    print(f"📊 坐标轴数量: {len(self.figure.axes)}")
                    print(f"📊 第一个轴中的线条数量: {len(ax.lines)}")
                
                self.current_file = file_path
                # 保存目录位置
                self.config_manager.set_last_directory(str(Path(file_path).parent))
                
                # 添加到文件标签页
                self.file_tab_widget.addFile(file_path)
                
                # 重置算法选择状态
                self.current_algorithm = "all"
                if hasattr(self, 'selected_algorithm_label'):
                    self.selected_algorithm_label.setText("当前: 所有算法")
                
                print(f"=== openFile: 准备加载文件 {Path(file_path).name} ===")
                
                self.setupPlotCanvas()
                self.loadChartProperties()
                self.updateUI()
                
                # 延迟调整大小，确保界面完全加载后再调整
                QTimer.singleShot(500, self.adjustCanvasSize)
                
                QMessageBox.information(self, "成功", f"成功加载文件: {Path(file_path).name}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法加载文件: {str(e)}")
                import traceback
                traceback.print_exc()  # 打印完整的错误堆栈

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        
        # 防止频繁调整 - 使用防抖机制
        if hasattr(self, 'canvas') and self.canvas:
            # 取消之前的定时器
            if hasattr(self, '_resize_timer'):
                self._resize_timer.stop()
            
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self._handleDelayedResize)
            self._resize_timer.start(300)  # 300ms防抖延迟
    
    def _handleDelayedResize(self):
        """延迟处理窗口大小调整"""
        try:
            if hasattr(self, 'canvas') and self.canvas:
                # 强制清理画布缓冲区
                self.canvas.flush_events()
                
                # 调整画布大小
                self.adjustCanvasSize()
                
                # 强制完整重绘
                if self.figure:
                    self.canvas.draw()
        except Exception as e:
            print(f"延迟调整大小失败: {e}")

    def saveCurrentFileState(self):
        """保存当前文件状态到内存"""
        if not self.current_file or not self.figure:
            return
        
        try:
            # 保存当前状态到内存
            file_state = {
                'figure': copy.deepcopy(self.figure),
                'last_modified': datetime.now(),
                'chart_properties': self.getCurrentChartProperties(),
                'algorithm_styles': self.getCurrentAlgorithmStyles(),
                'temp_saved': False
            }
            
            self.opened_files[self.current_file] = file_state
            print(f"保存文件状态: {Path(self.current_file).name}")
            
        except Exception as e:
            print(f"保存文件状态失败: {e}")

    def saveCurrentFileToTemp(self):
        """将当前文件保存到临时文件"""
        if not self.current_file or not self.figure or not self.temp_save_dir:
            return None
        
        try:
            # 应用当前设置到图表
            self._applyCurrentSettings()
            
            # 生成临时文件名
            file_name = Path(self.current_file).stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file = Path(self.temp_save_dir) / f"{file_name}_{timestamp}_temp.pkl"
            
            # 保存到临时文件
            with open(temp_file, 'wb') as f:
                pickle.dump(self.figure, f)
            
            # 更新文件状态
            if self.current_file in self.opened_files:
                self.opened_files[self.current_file]['temp_saved'] = True
                self.opened_files[self.current_file]['temp_file'] = str(temp_file)
            
            print(f"临时保存文件: {temp_file}")
            return str(temp_file)
            
        except Exception as e:
            print(f"临时保存失败: {e}")
            return None

    def getCurrentChartProperties(self):
        """获取当前图表属性"""
        if not self.figure or len(self.figure.axes) == 0:
            return {}
        
        ax = self.figure.axes[0]
        properties = {}
        
        try:
            properties['title'] = ax.get_title()
            properties['xlabel'] = ax.get_xlabel()
            properties['ylabel'] = ax.get_ylabel()
            properties['xscale'] = ax.get_xscale()
            properties['yscale'] = ax.get_yscale()
            properties['xlim'] = ax.get_xlim()
            properties['ylim'] = ax.get_ylim()
            properties['grid'] = ax.get_axisbelow() is not None
            
            # 图例属性
            legend = ax.get_legend()
            if legend:
                properties['legend_visible'] = legend.get_visible()
                properties['legend_location'] = legend._loc
                properties['legend_fontsize'] = legend.get_fontsize()
            else:
                properties['legend_visible'] = False
                
        except Exception as e:
            print(f"获取图表属性失败: {e}")
        
        return properties

    def getCurrentAlgorithmStyles(self):
        """获取当前所有算法样式"""
        if not self.figure or len(self.figure.axes) == 0:
            return {}
        
        ax = self.figure.axes[0]
        styles = {}
        
        for line in ax.lines:
            algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
            if algorithm_name:
                try:
                    styles[algorithm_name] = {
                        'color': line.get_color(),
                        'linewidth': line.get_linewidth(),
                        'linestyle': line.get_linestyle(),
                        'marker': line.get_marker(),
                        'markersize': line.get_markersize(),
                        'alpha': line.get_alpha() if line.get_alpha() is not None else 1.0,
                        'visible': line.get_visible(),
                        'zorder': line.get_zorder()
                    }
                except Exception as e:
                    print(f"获取算法样式失败 {algorithm_name}: {e}")
        
        return styles

    def switchToFile(self, file_path):
        """切换到指定文件"""
        if file_path == self.current_file:
            return  # 已经是当前文件
        
        # 保存当前文件状态
        if self.current_file:
            self.saveCurrentFileState()
            # 临时保存当前文件
            self.saveCurrentFileToTemp()
        
        # 切换到新文件
        try:
            if file_path in self.opened_files:
                # 从内存加载文件状态
                file_state = self.opened_files[file_path]
                self.figure = file_state['figure']
                print(f"从内存加载文件: {Path(file_path).name}")
            else:
                # 从磁盘加载文件
                with open(file_path, 'rb') as f:
                    self.figure = pickle.load(f)
                print(f"从磁盘加载文件: {Path(file_path).name}")
            
            self.current_file = file_path
            
            # 重置算法选择状态
            self.current_algorithm = "all"
            if hasattr(self, 'selected_algorithm_label'):
                self.selected_algorithm_label.setText("当前: 所有算法")
            
            # 重新设置界面
            self.setupPlotCanvas()
            self.loadChartProperties()
            self.updateUI()
            
            # 更新文件标签页
            self.file_tab_widget.setCurrentFile(file_path)
            
            # 延迟调整大小
            QTimer.singleShot(500, self.adjustCanvasSize)
            
            print(f"成功切换到文件: {Path(file_path).name}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换文件失败: {str(e)}")
            print(f"切换文件失败: {e}")

    def closeFile(self, file_path):
        """关闭指定文件"""
        if not file_path:
            return
        
        # 如果是当前文件，需要特殊处理
        if file_path == self.current_file:
            # 检查是否还有其他文件
            all_files = self.file_tab_widget.getAllFiles()
            other_files = [f for f in all_files if f != file_path]
            
            if other_files:
                # 切换到其他文件
                next_file = other_files[0]
                self.switchToFile(next_file)
            else:
                # 没有其他文件，清空界面
                self.clearCurrentFile()
        
        # 从文件列表移除
        self.file_tab_widget.removeFile(file_path)
        
        # 从内存中移除文件状态
        if file_path in self.opened_files:
            del self.opened_files[file_path]
        
        print(f"关闭文件: {Path(file_path).name}")

    def closeAllFiles(self):
        """关闭所有文件"""
        reply = QMessageBox.question(
            self, '确认关闭', 
            '确定要关闭所有文件吗？\n未保存的修改将会丢失。',
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 清空所有文件
            self.opened_files.clear()
            self.file_tab_widget.file_list.clear()
            self.file_tab_widget.updateButtonState()
            self.clearCurrentFile()
            print("关闭所有文件")

    def clearCurrentFile(self):
        """清空当前文件"""
        self.current_file = None
        self.figure = None
        
        # 清空界面
        if hasattr(self, 'plot_layout'):
            for i in reversed(range(self.plot_layout.count())):
                widget = self.plot_layout.itemAt(i).widget()
                if widget:
                    widget.setParent(None)
        
        # 清空算法列表和属性
        self.clearLineWidgets()
        
        if hasattr(self, 'algorithm_list'):
            self.algorithm_list.clear()
        
        if hasattr(self, 'selected_algorithm_label'):
            self.selected_algorithm_label.setText("当前: 无文件")
        
        # 清空图表属性
        if hasattr(self, 'title_edit'):
            self.title_edit.clear()
            self.xlabel_edit.clear()
            self.ylabel_edit.clear()
        
        print("清空当前文件")

    def closeCurrentFile(self):
        """关闭当前文件（菜单命令）"""
        if self.current_file:
            self.closeFile(self.current_file)
    
    def setupPlotCanvas(self):
        """设置图表画布"""
        # 清除旧的画布
        for i in reversed(range(self.plot_layout.count())):
            self.plot_layout.itemAt(i).widget().setParent(None)
        
        # 调整图表边距确保标签完全可见
        if self.figure and len(self.figure.axes) > 0:
            self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.9)
        
        # 自动应用保存的样式
        self.applyStoredStyles()
        
        # 自适应调整图表大小
        self.adjustFigureSize()
        
        # 创建新的画布
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        # 设置画布大小策略
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置画布属性以减少重绘问题
        self.canvas.setFocusPolicy(Qt.StrongFocus)
        
        # 连接鼠标点击事件
        self.canvas.mpl_connect('button_press_event', self.onCanvasClick)
        
        # 连接画布大小改变事件
        self.canvas.mpl_connect('resize_event', self.onCanvasResize)
        
        self.plot_layout.addWidget(self.toolbar)
        self.plot_layout.addWidget(self.canvas)
        
        # 初始化画布状态
        self._initializeCanvas()
        
        # 延迟调整以确保布局完成
        QTimer.singleShot(100, self.adjustCanvasSize)
    
    def _initializeCanvas(self):
        """初始化画布状态"""
        try:
            if self.canvas and self.figure:
                # 清理任何现有的缓冲区
                self.canvas.flush_events()
                
                # 设置合适的渲染选项
                self.figure.patch.set_facecolor('white')
                
                # 强制完整重绘
                self.canvas.draw()
                
                print("画布初始化完成")
        except Exception as e:
            print(f"画布初始化失败: {e}")
    
    def applyStoredStyles(self):
        """自动应用保存的样式"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        ax = self.figure.axes[0]
        lines = ax.lines
        
        # 获取所有保存的样式
        stored_styles = self.config_manager.get_all_algorithm_styles()
        
        if not stored_styles:
            return
        
        applied_count = 0
        
        for line in lines:
            # 获取线条的有效算法名称
            algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
            
            if algorithm_name and algorithm_name in stored_styles:
                style = stored_styles[algorithm_name]
                
                # 应用样式
                try:
                    if 'color' in style:
                        line.set_color(style['color'])
                    if 'linewidth' in style:
                        line.set_linewidth(style['linewidth'])
                    if 'linestyle' in style:
                        line.set_linestyle(style['linestyle'])
                    if 'marker' in style:
                        line.set_marker(style['marker'])
                    if 'markersize' in style:
                        line.set_markersize(style['markersize'])
                    if 'alpha' in style:
                        line.set_alpha(style['alpha'])
                    if 'visible' in style:
                        line.set_visible(style['visible'])  # 应用可见性
                    if 'zorder' in style:
                        line.set_zorder(style['zorder'])  # 应用图层顺序
                    
                    applied_count += 1
                except Exception as e:
                    print(f"应用样式失败 {algorithm_name}: {str(e)}")
        
        if applied_count > 0:
            print(f"自动应用了 {applied_count} 个算法的保存样式")
    
    def _getEffectiveAlgorithmNameForLine(self, line):
        """为线条获取有效的算法名称"""
        # 首先尝试从线条标签获取
        label = line.get_label()
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 如果标签无效，尝试从图例获取
        try:
            if hasattr(line, 'axes') and line.axes:
                ax = line.axes
                legend = ax.get_legend()
                if legend:
                    # 找到这条线在lines列表中的索引
                    lines = ax.lines
                    line_index = -1
                    for i, l in enumerate(lines):
                        if l is line:
                            line_index = i
                            break
                    
                    # 获取对应的图例文本
                    if line_index >= 0 and len(legend.get_texts()) > line_index:
                        legend_text = legend.get_texts()[line_index].get_text()
                        if legend_text and not legend_text.startswith('_') and legend_text.strip():
                            return legend_text.strip()
        except:
            pass
        
        return None
    
    def loadChartProperties(self):
        """加载图表属性"""
        print("=== loadChartProperties: 开始加载图表属性 ===")
        
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            print(f"=== loadChartProperties: 图表中共有 {len(lines)} 条线 ===")
            
            # 标题
            title = ax.get_title()
            self.title_edit.setText(title)
            
            # 轴标签
            xlabel = ax.get_xlabel()
            ylabel = ax.get_ylabel()
            self.xlabel_edit.setText(xlabel)
            self.ylabel_edit.setText(ylabel)
            
            # 网格
            grid_visible = ax.grid
            self.grid_check.setChecked(bool(grid_visible))
            
            # 坐标轴刻度类型
            xscale = ax.get_xscale()
            yscale = ax.get_yscale()
            
            xscale_index = self.xscale_combo.findText(xscale)
            if xscale_index >= 0:
                self.xscale_combo.setCurrentIndex(xscale_index)
            
            yscale_index = self.yscale_combo.findText(yscale)
            if yscale_index >= 0:
                self.yscale_combo.setCurrentIndex(yscale_index)
            
            # 坐标轴范围
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            self.xmin_edit.setText(f"{xlim[0]:.3f}")
            self.xmax_edit.setText(f"{xlim[1]:.3f}")
            self.ymin_edit.setText(f"{ylim[0]:.3f}")
            self.ymax_edit.setText(f"{ylim[1]:.3f}")
            
            # 字体大小设置
            try:
                # 获取刻度字体大小
                tick_fontsize = ax.tick_params()  # 这个方法不返回值，需要用其他方式
                # 从第一个刻度标签获取字体大小
                tick_labels = ax.get_xticklabels()
                if tick_labels:
                    tick_fontsize = tick_labels[0].get_fontsize()
                    self.tick_fontsize_spin.setValue(tick_fontsize)
                
                # 获取标签字体大小
                xlabel_fontsize = ax.xaxis.label.get_fontsize()
                self.label_fontsize_spin.setValue(xlabel_fontsize)
            except:
                # 如果获取失败，使用默认值
                self.tick_fontsize_spin.setValue(10)
                self.label_fontsize_spin.setValue(12)
            
            # 图例设置
            legend = ax.get_legend()
            if legend:
                self.legend_check.setChecked(True)
                
                # 获取图例位置 - 检查是否为外部位置
                bbox = legend.get_bbox_to_anchor()
                loc = legend._loc
                
                # 判断是否为外部位置
                if bbox is not None:
                    x, y = bbox.x0, bbox.y0
                    if x > 1.0:  # 右侧外部
                        self.legend_loc_combo.setCurrentText("outside right")
                    elif x < 0.0:  # 左侧外部
                        self.legend_loc_combo.setCurrentText("outside left")
                    elif y > 1.0:  # 顶部外部
                        self.legend_loc_combo.setCurrentText("outside top")
                    elif y < 0.0:  # 底部外部
                        self.legend_loc_combo.setCurrentText("outside bottom")
                    else:
                        # 标准位置
                        if isinstance(loc, str):
                            loc_index = self.legend_loc_combo.findText(loc)
                            if loc_index >= 0:
                                self.legend_loc_combo.setCurrentIndex(loc_index)
                else:
                    # 标准位置
                    if isinstance(loc, str):
                        loc_index = self.legend_loc_combo.findText(loc)
                        if loc_index >= 0:
                            self.legend_loc_combo.setCurrentIndex(loc_index)
                
                # 获取图例字体大小
                if legend.get_texts():
                    fontsize = legend.get_texts()[0].get_fontsize()
                    self.legend_fontsize_spin.setValue(fontsize)
                
                # 获取图例透明度
                alpha = legend.get_frame().get_alpha()
                if alpha is not None:
                    self.legend_alpha_slider.setValue(int(alpha * 100))
            else:
                self.legend_check.setChecked(False)
            
            # 加载算法列表
            self.loadAlgorithmList()
            
            print("=== loadChartProperties: 准备调用 loadLineProperties ===")
            # 加载线条属性控件
            self.loadLineProperties()
            print("=== loadChartProperties: loadLineProperties 调用完成 ===")
    
    def loadAlgorithmList(self):
        """加载算法列表到弹出菜单"""
        self.algorithm_menu.clear()
        
        # 添加"所有算法"选项
        all_action = self.algorithm_menu.addAction("所有算法")
        all_action.triggered.connect(lambda: self.selectAlgorithm("all", "所有算法"))
        
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            
            # 从图例中获取算法名称
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    algorithm_name = text.get_text()
                    if algorithm_name and not algorithm_name.startswith('_'):
                        action = self.algorithm_menu.addAction(algorithm_name)
                        action.triggered.connect(lambda checked, name=algorithm_name: self.selectAlgorithm(name, name))
            else:
                # 如果没有图例，从线条标签获取
                for line in lines:
                    label = line.get_label()
                    if label and not label.startswith('_'):
                        action = self.algorithm_menu.addAction(label)
                        action.triggered.connect(lambda checked, name=label: self.selectAlgorithm(name, name))
    
    def selectAlgorithm(self, algorithm_id, algorithm_name):
        """选择算法"""
        self.current_algorithm = algorithm_id
        self.selected_algorithm_label.setText(f"当前: {algorithm_name}")
        
        # 只需要更新可见性，不需要重新创建控件
        if hasattr(self, 'all_line_widgets') and self.all_line_widgets:
            self.updateWidgetVisibility()
            self.line_widgets = [w for w in self.all_line_widgets if w.isVisible()]
    
    def loadLineProperties(self):
        """加载线条属性"""
        # 清除所有旧的线条控件
        self.clearLineWidgets()
        
        # 初始化控件列表
        if not hasattr(self, 'all_line_widgets'):
            self.all_line_widgets = []
        
        # 创建新的控件
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            
            print(f"正在为 {len(lines)} 条线创建控件...")
            
            # ======== DEBUG 断点位置 1: 分析数据来源 ========
            print("=== 数据来源分析 ===")
            print(f"图表对象ID: {id(self.figure)}")
            print(f"坐标轴对象ID: {id(ax)}")
            print(f"线条列表对象ID: {id(lines)}")
            
            # 检查是否有图例
            legend = ax.get_legend()
            if legend:
                legend_texts = [text.get_text() for text in legend.get_texts()]
                print(f"图例中的文本: {legend_texts}")
                print(f"图例文本数量: {len(legend_texts)}")
            else:
                print("没有图例")
            
            # ======== DEBUG 断点位置 2: 分析每条线的数据 ========
            print("=== 线条数据分析 ===")
            for i, line in enumerate(lines):
                xdata = line.get_xdata()
                ydata = line.get_ydata()
                label = line.get_label()
                
                # 计算数据哈希值来检测完全相同的数据
                import hashlib
                data_hash = hashlib.md5(f"{list(xdata)}{list(ydata)}".encode()).hexdigest()[:8]
                
                print(f"线条 {i}:")
                print(f"  - 标签: {label}")
                print(f"  - 数据点数: {len(xdata)}")
                if len(xdata) > 0 and len(ydata) > 0:
                    print(f"  - X范围: [{min(xdata):.6f}, {max(xdata):.6f}]")
                    print(f"  - Y范围: [{min(ydata):.6f}, {max(ydata):.6f}]")
                else:
                    print(f"  - X范围: [空数据]")
                    print(f"  - Y范围: [空数据]")
                print(f"  - 数据哈希: {data_hash}")
                print(f"  - 颜色: {line.get_color()}")
                print(f"  - 线对象ID: {id(line)}")
            
            # 用于检测重复算法和数据
            algorithm_count = {}
            data_hashes = {}
            
            for i, line in enumerate(lines):
                # 获取算法名称用于调试
                algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
                if not algorithm_name:
                    algorithm_name = f"Line {i + 1}"
                
                # 计算数据哈希
                xdata = line.get_xdata()
                ydata = line.get_ydata()
                data_hash = hashlib.md5(f"{list(xdata)}{list(ydata)}".encode()).hexdigest()[:8]
                
                # ======== DEBUG 断点位置 3: 检测重复数据 ========
                if data_hash in data_hashes:
                    print(f"🔍 发现重复数据！线条 {i} 的数据哈希 {data_hash} 与线条 {data_hashes[data_hash]} 相同")
                    print(f"   算法名称: {algorithm_name}")
                else:
                    data_hashes[data_hash] = i
                
                # 记录算法出现次数
                if algorithm_name in algorithm_count:
                    algorithm_count[algorithm_name] += 1
                    print(f"  创建控件 {i}: {algorithm_name} (重复第{algorithm_count[algorithm_name]}次, 数据哈希: {data_hash})")
                else:
                    algorithm_count[algorithm_name] = 1
                    print(f"  创建控件 {i}: {algorithm_name} (首次出现, 数据哈希: {data_hash})")
                
                # ======== DEBUG 断点位置 4: 创建控件前 ========
                # 在这里可以设置断点来检查每个控件创建前的状态
                
                line_widget = LinePropertiesWidget(line, i, self.config_manager)
                line_widget.propertiesChanged.connect(self.onLinePropertiesChanged)
                line_widget.styleChanged.connect(self.onStyleChanged)
                self.properties_layout.addWidget(line_widget)
                self.all_line_widgets.append(line_widget)
                
                # 加载线条属性到控件中，确保显示当前的样式
                line_widget.loadLineProperties()
            
            # 打印重复算法统计
            print("=== 算法重复统计 ===")
            for algo_name, count in algorithm_count.items():
                if count > 1:
                    print(f"  ⚠️  算法 '{algo_name}' 重复了 {count} 次")
                else:
                    print(f"  ✓  算法 '{algo_name}' 只出现1次")
            
            # ======== DEBUG 断点位置 5: 数据重复统计 ========
            print("=== 数据重复统计 ===")
            hash_count = {}
            for data_hash, line_index in data_hashes.items():
                if data_hash in hash_count:
                    hash_count[data_hash] += 1
                else:
                    hash_count[data_hash] = 1
            
            for data_hash, count in hash_count.items():
                if count > 1:
                    print(f"  ⚠️  数据哈希 {data_hash} 出现了 {count} 次 (完全相同的数据)")
        
        print(f"共创建了 {len(self.all_line_widgets)} 个控件")
        
        # 根据当前选择的算法显示/隐藏控件
        self.updateWidgetVisibility()
        
        # 更新line_widgets列表以保持兼容性
        self.line_widgets = [w for w in self.all_line_widgets if w.isVisible()]
        
        print(f"当前显示 {len(self.line_widgets)} 个控件 (选择的算法: {self.current_algorithm})")
    
    def clearLineWidgets(self):
        """清除所有线条控件"""
        widget_count = 0
        if hasattr(self, 'all_line_widgets'):
            widget_count = len(self.all_line_widgets)
            if widget_count > 0:
                print(f"正在清除 {widget_count} 个控件...")
            
            for widget in self.all_line_widgets:
                # 断开信号连接
                try:
                    widget.propertiesChanged.disconnect()
                    widget.styleChanged.disconnect()
                except:
                    pass
                # 从布局中移除并删除控件
                self.properties_layout.removeWidget(widget)
                widget.setParent(None)
                widget.deleteLater()
            self.all_line_widgets.clear()
        
        if hasattr(self, 'line_widgets'):
            self.line_widgets.clear()
        
        if widget_count > 0:
            print(f"已清除 {widget_count} 个控件")
    
    def updateWidgetVisibility(self):
        """更新控件可见性"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            selected_algorithm = self.current_algorithm
            
            for i, widget in enumerate(self.all_line_widgets):
                if i < len(lines):
                    line = lines[i]
                    
                    # 获取有效的算法名称
                    algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
                    if not algorithm_name:
                        algorithm_name = f"Line {i + 1}"
                    
                    # 决定是否显示该控件
                    if selected_algorithm == "all":
                        widget.show()
                    else:
                        if algorithm_name == selected_algorithm:
                            widget.show()
                        else:
                            widget.hide()
    
    def updateUI(self):
        """更新UI状态"""
        has_figure = self.figure is not None
        self.save_button.setEnabled(has_figure)
        self.export_button.setEnabled(has_figure)
        self.save_styles_button.setEnabled(has_figure)
    
    def onTitleChanged(self, text):
        """标题改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_title(text)
            self.canvas.draw_idle()
    
    def onXLabelChanged(self, text):
        """X轴标签改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_xlabel(text)
            self.canvas.draw_idle()
    
    def onYLabelChanged(self, text):
        """Y轴标签改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_ylabel(text)
            self.canvas.draw_idle()
    
    def onGridToggled(self, checked):
        """网格切换"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].grid(checked)
            self.canvas.draw_idle()
    
    def onLegendToggled(self, checked):
        """图例显示切换"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            if checked:
                ax.legend()
            else:
                legend = ax.get_legend()
                if legend:
                    legend.remove()
            self.canvas.draw_idle()
    
    def onLegendLocationChanged(self, location):
        """图例位置改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                # 处理外部位置
                if location.startswith("outside"):
                    if location == "outside right":
                        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
                    elif location == "outside left":
                        ax.legend(bbox_to_anchor=(-0.05, 1), loc='upper right')
                    elif location == "outside top":
                        ax.legend(bbox_to_anchor=(0.5, 1.05), loc='lower center')
                    elif location == "outside bottom":
                        ax.legend(bbox_to_anchor=(0.5, -0.05), loc='upper center')
                    
                    # 调整图表布局以适应外部图例
                    if location == "outside right":
                        self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.8, top=0.9)
                    elif location == "outside left":
                        self.figure.subplots_adjust(left=0.25, bottom=0.1, right=0.95, top=0.9)
                    elif location == "outside top":
                        self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.85)
                    elif location == "outside bottom":
                        self.figure.subplots_adjust(left=0.15, bottom=0.2, right=0.95, top=0.9)
                else:
                    # 标准位置
                    ax.legend(loc=location)
                    # 恢复默认布局
                    self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.9)
                
                self.canvas.draw_idle()
    
    def onLegendFontsizeChanged(self, fontsize):
        """图例字体大小改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    text.set_fontsize(fontsize)
                self.canvas.draw_idle()
    
    def onLegendAlphaChanged(self, alpha_percent):
        """图例透明度改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                alpha = alpha_percent / 100.0
                legend.get_frame().set_alpha(alpha)
                self.canvas.draw_idle()
    
    def _clipDataForLogScale(self, ax, axis='x', log_threshold=1e-8):
        """为对数刻度截止数据中的零值和极小值"""
        clipped_count = 0
        
        for line in ax.lines:
            if axis == 'x':
                data = line.get_xdata()
                original_data = np.array(data)
                # 截止处理
                clipped_data = np.where(original_data <= 0, log_threshold, original_data)
                clipped_data = np.where(clipped_data < log_threshold, log_threshold, clipped_data)
                line.set_xdata(clipped_data)
                # 统计被截止的数据点
                clipped_count += np.sum((original_data <= 0) | ((original_data > 0) & (original_data < log_threshold)))
            else:  # axis == 'y'
                data = line.get_ydata()
                original_data = np.array(data)
                # 截止处理
                clipped_data = np.where(original_data <= 0, log_threshold, original_data)
                clipped_data = np.where(clipped_data < log_threshold, log_threshold, clipped_data)
                line.set_ydata(clipped_data)
                # 统计被截止的数据点
                clipped_count += np.sum((original_data <= 0) | ((original_data > 0) & (original_data < log_threshold)))
        
        return clipped_count

    def onXScaleChanged(self, scale):
        """X轴刻度类型改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            try:
                # 获取当前数据范围
                current_xlim = ax.get_xlim()
                
                # 如果切换到对数刻度，先处理数据
                log_threshold = 1e-8
                if scale == 'log':
                    clipped_count = self._clipDataForLogScale(ax, 'x', log_threshold)
                    if clipped_count > 0:
                        QMessageBox.information(self, "提示", 
                            f"X轴切换到对数刻度：{clipped_count} 个数据点（≤0或<{log_threshold}）已截止到 {log_threshold}")
                
                # 设置新的刻度类型
                ax.set_xscale(scale)
                
                # 根据刻度类型自适应调整范围
                if scale == 'log':
                    # 对数刻度：数据已经被预处理，直接计算范围
                    x_data = []
                    for line in ax.lines:
                        x_line_data = line.get_xdata()
                        x_data.extend(x_line_data)
                    
                    if len(x_data) > 0:
                        x_min = min(x_data)
                        x_max = max(x_data)
                        
                        # 为对数刻度添加一些边距
                        log_margin = 0.1  # 10%的对数边距
                        x_min_log = x_min / (10 ** log_margin)
                        x_max_log = x_max * (10 ** log_margin)
                        
                        # 确保最小值不小于阈值
                        x_min_log = max(x_min_log, log_threshold)
                        
                        ax.set_xlim(x_min_log, x_max_log)
                        
                        # 更新输入框显示
                        self.xmin_edit.setText(f"{x_min_log:.6g}")
                        self.xmax_edit.setText(f"{x_max_log:.6g}")
                    else:
                        # 如果没有数据，设置默认的对数范围
                        ax.set_xlim(log_threshold, 10)
                        self.xmin_edit.setText(f"{log_threshold:.6g}")
                        self.xmax_edit.setText("10")
                        QMessageBox.information(self, "提示", 
                            f"X轴数据为空，已设置默认对数范围 [{log_threshold:.6g}, 10]")
                
                elif scale == 'linear':
                    # 切换到线性刻度：使用所有数据重新计算范围
                    x_data = []
                    for line in ax.lines:
                        x_line_data = line.get_xdata()
                        # 转换为numpy数组以确保一致性
                        x_line_data = np.array(x_line_data)
                        x_data.extend(x_line_data)
                    
                    if len(x_data) > 0:
                        x_min = min(x_data)
                        x_max = max(x_data)
                        
                        # 为线性刻度添加5%的边距
                        linear_margin = 0.05
                        x_range = x_max - x_min
                        if x_range > 0:
                            x_min_linear = x_min - x_range * linear_margin
                            x_max_linear = x_max + x_range * linear_margin
                        else:
                            # 如果数据范围为0，设置小的默认范围
                            x_min_linear = x_min - 1
                            x_max_linear = x_max + 1
                        
                        ax.set_xlim(x_min_linear, x_max_linear)
                        
                        # 更新输入框显示
                        self.xmin_edit.setText(f"{x_min_linear:.6g}")
                        self.xmax_edit.setText(f"{x_max_linear:.6g}")
                
                self.canvas.draw_idle()
                
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法设置X轴刻度为{scale}: {str(e)}")
    
    def onYScaleChanged(self, scale):
        """Y轴刻度类型改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            try:
                # 获取当前数据范围
                current_ylim = ax.get_ylim()
                
                # 如果切换到对数刻度，先处理数据
                log_threshold = 1e-8
                if scale == 'log':
                    clipped_count = self._clipDataForLogScale(ax, 'y', log_threshold)
                    if clipped_count > 0:
                        QMessageBox.information(self, "提示", 
                            f"Y轴切换到对数刻度：{clipped_count} 个数据点（≤0或<{log_threshold}）已截止到 {log_threshold}")
                
                # 设置新的刻度类型
                ax.set_yscale(scale)
                
                # 根据刻度类型自适应调整范围
                if scale == 'log':
                    # 对数刻度：数据已经被预处理，直接计算范围
                    y_data = []
                    for line in ax.lines:
                        y_line_data = line.get_ydata()
                        y_data.extend(y_line_data)
                    
                    if len(y_data) > 0:
                        y_min = min(y_data)
                        y_max = max(y_data)
                        
                        # 为对数刻度添加一些边距
                        log_margin = 0.1  # 10%的对数边距
                        y_min_log = y_min / (10 ** log_margin)
                        y_max_log = y_max * (10 ** log_margin)
                        
                        # 确保最小值不小于阈值
                        y_min_log = max(y_min_log, log_threshold)
                        
                        ax.set_ylim(y_min_log, y_max_log)
                        
                        # 更新输入框显示
                        self.ymin_edit.setText(f"{y_min_log:.6g}")
                        self.ymax_edit.setText(f"{y_max_log:.6g}")
                    else:
                        # 如果没有数据，设置默认的对数范围
                        ax.set_ylim(log_threshold, 10)
                        self.ymin_edit.setText(f"{log_threshold:.6g}")
                        self.ymax_edit.setText("10")
                        QMessageBox.information(self, "提示", 
                            f"Y轴数据为空，已设置默认对数范围 [{log_threshold:.6g}, 10]")
                
                elif scale == 'linear':
                    # 切换到线性刻度：使用所有数据重新计算范围
                    y_data = []
                    for line in ax.lines:
                        y_line_data = line.get_ydata()
                        # 转换为numpy数组以确保一致性
                        y_line_data = np.array(y_line_data)
                        y_data.extend(y_line_data)
                    
                    if len(y_data) > 0:
                        y_min = min(y_data)
                        y_max = max(y_data)
                        
                        # 为线性刻度添加5%的边距
                        linear_margin = 0.05
                        y_range = y_max - y_min
                        if y_range > 0:
                            y_min_linear = y_min - y_range * linear_margin
                            y_max_linear = y_max + y_range * linear_margin
                        else:
                            # 如果数据范围为0，设置小的默认范围
                            y_min_linear = y_min - 1
                            y_max_linear = y_max + 1
                        
                        ax.set_ylim(y_min_linear, y_max_linear)
                        
                        # 更新输入框显示
                        self.ymin_edit.setText(f"{y_min_linear:.6g}")
                        self.ymax_edit.setText(f"{y_max_linear:.6g}")
                
                self.canvas.draw_idle()
                
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法设置Y轴刻度为{scale}: {str(e)}")
    
    def onXRangeChanged(self):
        """X轴范围改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                xmin_text = self.xmin_edit.text().strip()
                xmax_text = self.xmax_edit.text().strip()
                
                if xmin_text and xmax_text:
                    xmin = float(xmin_text)
                    xmax = float(xmax_text)
                    if xmin < xmax:
                        self.figure.axes[0].set_xlim(xmin, xmax)
                        self.canvas.draw_idle()
            except ValueError:
                pass  # 忽略无效输入
    
    def onYRangeChanged(self):
        """Y轴范围改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                ymin_text = self.ymin_edit.text().strip()
                ymax_text = self.ymax_edit.text().strip()
                
                if ymin_text and ymax_text:
                    ymin = float(ymin_text)
                    ymax = float(ymax_text)
                    if ymin < ymax:
                        self.figure.axes[0].set_ylim(ymin, ymax)
                        self.canvas.draw_idle()
            except ValueError:
                pass  # 忽略无效输入
    
    def onAutoRange(self):
        """自动设置坐标轴范围"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            ax.relim()
            ax.autoscale()
            
            # 更新范围输入框
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            self.xmin_edit.setText(f"{xlim[0]:.3f}")
            self.xmax_edit.setText(f"{xlim[1]:.3f}")
            self.ymin_edit.setText(f"{ylim[0]:.3f}")
            self.ymax_edit.setText(f"{ylim[1]:.3f}")
            
            self.canvas.draw_idle()
    
    def normalizeText(self, line_edit):
        """规范化文本：只有第一个单词首字母大写，下划线替换为空格"""
        text = line_edit.text()
        if not text:
            return
        
        # 将下划线替换为空格
        normalized = text.replace('_', ' ')
        
        # 将整个字符串转为小写，然后只将第一个字符大写
        if normalized:
            normalized = normalized.lower()
            normalized = normalized[0].upper() + normalized[1:]
        
        line_edit.setText(normalized)
    
    def normalizeAllLabels(self):
        """统一规范化标题、X轴标签和Y轴标签"""
        self.normalizeText(self.title_edit)
        self.normalizeText(self.xlabel_edit)
        self.normalizeText(self.ylabel_edit)
    
    def onTickFontsizeChanged(self, fontsize):
        """坐标轴刻度字体大小改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            ax.tick_params(axis='both', which='major', labelsize=fontsize)
            self.canvas.draw_idle()
    
    def onLabelFontsizeChanged(self, fontsize):
        """坐标轴标签字体大小改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            ax.xaxis.label.set_fontsize(fontsize)
            ax.yaxis.label.set_fontsize(fontsize)
            ax.title.set_fontsize(fontsize + 2)  # 标题稍大一些
            self.canvas.draw_idle()
    
    def editLegendNames(self):
        """编辑图例名称"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可编辑的图表")
            return
        
        ax = self.figure.axes[0]
        legend = ax.get_legend()
        
        if not legend:
            QMessageBox.warning(self, "警告", "图表中没有图例")
            return
        
        # 创建图例编辑对话框
        dialog = LegendEditDialog(ax, self)
        if dialog.exec_() == QDialog.Accepted:
            # 重新加载线条属性以反映名称变化
            self.loadLineProperties()
            self.canvas.draw_idle()
    
    def adjustLayerOrder(self):
        """调整图层顺序"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可调整的图表")
            return
        
        ax = self.figure.axes[0]
        if len(ax.lines) == 0:
            QMessageBox.warning(self, "警告", "图表中没有线条")
            return
        
        # 创建图层顺序对话框
        dialog = LayerOrderDialog(ax, self)
        if dialog.exec_() == QDialog.Accepted:
            # 获取新的图层顺序
            new_order = dialog.getLayerOrder()
            
            # 保存图层顺序到样式配置中
            self.saveLayerOrderToStyles(ax, new_order)
            
            # 立即重绘画布显示图层变化
            if self.canvas:
                try:
                    # macOS特定优化
                    if platform.system() == 'Darwin':
                        self.canvas.flush_events()
                    
                    # 强制完整重绘，确保图层变化立即可见
                    self.canvas.draw()
                    print("图层顺序重绘完成")
                    
                except Exception as e:
                    print(f"重绘失败: {e}")
                    # 如果失败，尝试使用draw_idle
                    self.canvas.draw_idle()
            
            QMessageBox.information(self, "成功", f"图层顺序已更新！\n新顺序: {[i+1 for i in new_order]}")
    
    def saveLayerOrderToStyles(self, ax, layer_order):
        """将图层顺序保存到样式配置中"""
        try:
            lines = ax.lines
            legend = ax.get_legend()
            legend_texts = []
            if legend:
                legend_texts = [text.get_text() for text in legend.get_texts()]
            
            for display_order, original_index in enumerate(layer_order):
                if original_index < len(lines):
                    line = lines[original_index]
                    
                    # 获取算法名称
                    algorithm_name = ""
                    if original_index < len(legend_texts) and legend_texts[original_index]:
                        algorithm_name = legend_texts[original_index]
                    elif line.get_label() and not line.get_label().startswith('_'):
                        algorithm_name = line.get_label()
                    else:
                        algorithm_name = f"Line {original_index + 1}"
                    
                    if algorithm_name:
                        # 获取现有样式
                        existing_style = self.config_manager.get_algorithm_style(algorithm_name)
                        if existing_style is None:
                            # 如果没有现有样式，创建新的
                            existing_style = {
                                'color': line.get_color(),
                                'linewidth': line.get_linewidth(),
                                'linestyle': line.get_linestyle(),
                                'marker': line.get_marker(),
                                'markersize': line.get_markersize(),
                                'alpha': line.get_alpha() if line.get_alpha() is not None else 1.0,
                                'visible': line.get_visible()
                            }
                        
                        # 更新z-order
                        z_order = len(layer_order) - display_order
                        existing_style['zorder'] = z_order
                        
                        # 保存更新后的样式
                        self.config_manager.save_algorithm_style(algorithm_name, existing_style)
            
            print(f"图层顺序已保存到样式配置中: {layer_order}")
            
        except Exception as e:
            print(f"保存图层顺序失败: {e}")
    
    def onLinePropertiesChanged(self):
        """线条属性改变"""
        if self.canvas:
            # 更新图例样式
            self.updateLegendStyles()
            self.canvas.draw_idle()
    
    def updateLegendStyles(self):
        """更新图例样式以匹配线条样式"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            
            if legend:
                # 获取图例中的线条对象
                legend_lines = legend.get_lines()
                plot_lines = ax.lines
                
                # 确保图例线条数量与绘图线条数量匹配
                for i, (legend_line, plot_line) in enumerate(zip(legend_lines, plot_lines)):
                    # 同步样式
                    legend_line.set_color(plot_line.get_color())
                    legend_line.set_linewidth(plot_line.get_linewidth())
                    legend_line.set_linestyle(plot_line.get_linestyle())
                    legend_line.set_marker(plot_line.get_marker())
                    legend_line.set_markersize(plot_line.get_markersize())
                    legend_line.set_alpha(plot_line.get_alpha())
    
    def saveFile(self):
        """保存文件"""
        if self.current_file and self.figure:
            try:
                # 在保存前确保所有当前设置都应用到图表
                self._applyCurrentSettings()
                
                with open(self.current_file, 'wb') as f:
                    pickle.dump(self.figure, f)
                QMessageBox.information(self, "成功", "文件保存成功!")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def _applyCurrentSettings(self):
        """应用当前UI设置到图表"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        ax = self.figure.axes[0]
        
        # 应用标题和标签
        ax.set_title(self.title_edit.text())
        ax.set_xlabel(self.xlabel_edit.text())
        ax.set_ylabel(self.ylabel_edit.text())
        
        # 应用网格设置
        ax.grid(self.grid_check.isChecked())
        
        # 应用坐标轴刻度类型
        ax.set_xscale(self.xscale_combo.currentText())
        ax.set_yscale(self.yscale_combo.currentText())
        
        # 应用坐标轴范围
        try:
            xmin = float(self.xmin_edit.text())
            xmax = float(self.xmax_edit.text())
            if xmin < xmax:
                ax.set_xlim(xmin, xmax)
        except ValueError:
            pass
        
        try:
            ymin = float(self.ymin_edit.text())
            ymax = float(self.ymax_edit.text())
            if ymin < ymax:
                ax.set_ylim(ymin, ymax)
        except ValueError:
            pass
        
        # 应用图例设置
        if self.legend_check.isChecked():
            location = self.legend_loc_combo.currentText()
            if location.startswith("outside"):
                if location == "outside right":
                    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
                elif location == "outside left":
                    ax.legend(bbox_to_anchor=(-0.05, 1), loc='upper right')
                elif location == "outside top":
                    ax.legend(bbox_to_anchor=(0.5, 1.05), loc='lower center')
                elif location == "outside bottom":
                    ax.legend(bbox_to_anchor=(0.5, -0.05), loc='upper center')
            else:
                ax.legend(loc=location)
            
            # 应用图例字体大小和透明度
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    text.set_fontsize(self.legend_fontsize_spin.value())
                alpha = self.legend_alpha_slider.value() / 100.0
                legend.get_frame().set_alpha(alpha)
        
        # 应用字体大小设置
        ax.tick_params(axis='both', which='major', labelsize=self.tick_fontsize_spin.value())
        ax.xaxis.label.set_fontsize(self.label_fontsize_spin.value())
        ax.yaxis.label.set_fontsize(self.label_fontsize_spin.value())
        ax.title.set_fontsize(self.label_fontsize_spin.value() + 2)
    
    def saveAsFile(self):
        """另存为"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存图表文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
            
            if file_path:
                try:
                    with open(file_path, 'wb') as f:
                        pickle.dump(self.figure, f)
                    self.current_file = file_path
                    # 保存目录位置
                    self.config_manager.set_last_directory(str(Path(file_path).parent))
                    QMessageBox.information(self, "成功", "文件保存成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    

    
    def exportSVG(self):
        """导出为SVG"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出SVG图片", last_dir, "SVG Files (*.svg);;All Files (*)")
            
            if file_path:
                try:
                    self.figure.savefig(file_path, format='svg', bbox_inches='tight')
                    QMessageBox.information(self, "成功", "SVG导出成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def onStyleChanged(self, algorithm_name, style_dict):
        """样式改变回调"""
        print(f"算法 {algorithm_name} 的样式已保存: {style_dict}")
    
    def saveAllAlgorithmStyles(self):
        """保存当前所有算法的样式"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可保存的图表")
            return
        
        ax = self.figure.axes[0]
        lines = ax.lines
        
        if len(lines) == 0:
            QMessageBox.warning(self, "警告", "图表中没有线条")
            return
        
        try:
            saved_count = 0
            failed_algorithms = []
            
            # 遍历所有线条并保存样式
            for i, line in enumerate(lines):
                # 获取算法名称
                algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
                if not algorithm_name:
                    algorithm_name = f"Line {i + 1}"
                
                try:
                    # 获取当前样式
                    style = {
                        'color': line.get_color(),
                        'linewidth': line.get_linewidth(),
                        'linestyle': line.get_linestyle(),
                        'marker': line.get_marker(),
                        'markersize': line.get_markersize(),
                        'alpha': line.get_alpha() if line.get_alpha() is not None else 1.0,
                        'visible': line.get_visible(),
                        'zorder': line.get_zorder()
                    }
                    
                    # 保存到配置管理器
                    self.config_manager.save_algorithm_style(algorithm_name, style)
                    saved_count += 1
                    print(f"已保存算法样式: {algorithm_name}")
                    
                except Exception as e:
                    failed_algorithms.append(algorithm_name)
                    print(f"保存算法样式失败 {algorithm_name}: {str(e)}")
            
            # 显示保存结果
            if saved_count > 0:
                result_message = f"成功保存了 {saved_count} 个算法的样式！"
                
                if failed_algorithms:
                    result_message += f"\n失败的算法: {', '.join(failed_algorithms)}"
                    QMessageBox.warning(self, "部分成功", result_message)
                else:
                    QMessageBox.information(self, "保存成功", result_message)
            else:
                QMessageBox.warning(self, "保存失败", "没有成功保存任何算法样式")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存样式时发生错误: {str(e)}")
    
    def batchProcess(self):
        """批处理"""
        algorithm_styles = self.config_manager.get_all_algorithm_styles()
        
        if not algorithm_styles:
            QMessageBox.warning(self, "警告", "没有保存的算法样式。请先编辑图表并保存算法样式。")
            return
        
        # 打开批处理对话框
        dialog = BatchProcessDialog(algorithm_styles, self)
        if dialog.exec_() == QDialog.Accepted:
            files = dialog.getSelectedFiles()
            export_png = dialog.shouldExportPNG()
            export_pdf = dialog.shouldExportPDF()
            
            if not files:
                QMessageBox.warning(self, "警告", "没有选择要处理的文件。")
                return
            
            # 创建进度对话框
            progress_dialog = QProgressDialog("正在处理文件...", "取消", 0, 100, self)
            progress_dialog.setWindowTitle("批处理进度")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.show()
            
            # 创建批处理线程
            self.batch_thread = BatchProcessThread(files, algorithm_styles, export_png, export_pdf)
            self.batch_thread.progress.connect(progress_dialog.setValue)
            self.batch_thread.status.connect(lambda msg: progress_dialog.setLabelText(msg))
            self.batch_thread.finished_signal.connect(
                lambda success, total: self.onBatchProcessFinished(success, total, progress_dialog))
            
            # 连接取消按钮
            progress_dialog.canceled.connect(self.batch_thread.terminate)
            
            # 开始处理
            self.batch_thread.start()
    
    def onBatchProcessFinished(self, success_count, total_count, progress_dialog):
        """批处理完成"""
        progress_dialog.close()
        
        if success_count == total_count:
            QMessageBox.information(self, "成功", 
                f"批处理完成！成功处理了 {success_count} 个文件。")
        else:
            QMessageBox.warning(self, "部分成功", 
                f"批处理完成！成功处理了 {success_count}/{total_count} 个文件。")
    
    def onCanvasClick(self, event):
        """画布点击事件处理"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        # 只处理左键点击且在坐标轴内的点击
        if event.button != 1 or not event.inaxes:
            return
        
        ax = event.inaxes
        click_x, click_y = event.xdata, event.ydata
        
        if click_x is None or click_y is None:
            return
        
        # 查找最近的线段
        closest_line = None
        closest_distance = float('inf')
        closest_algorithm = None
        
        for i, line in enumerate(ax.lines):
            if not line.get_visible():
                continue
                
            # 获取线段数据
            xdata = np.array(line.get_xdata())
            ydata = np.array(line.get_ydata())
            
            if len(xdata) == 0 or len(ydata) == 0:
                continue
            
            # 计算点击位置到线段的最短距离
            min_distance = self.calculateDistanceToLine(click_x, click_y, xdata, ydata, ax)
            
            if min_distance < closest_distance:
                closest_distance = min_distance
                closest_line = line
                closest_algorithm = self._getEffectiveAlgorithmNameForLine(line)
                if not closest_algorithm:
                    closest_algorithm = f"Line {i + 1}"
        
        # 如果找到了足够近的线段（距离阈值可以调整）
        distance_threshold = 0.05  # 相对于坐标轴范围的5%
        if closest_line and closest_distance < distance_threshold:
            self.highlightSelectedLine(closest_line, closest_algorithm)
            self.selectAlgorithmInUI(closest_algorithm, closest_line)
    
    def calculateDistanceToLine(self, click_x, click_y, xdata, ydata, ax):
        """计算点击位置到线段的最短距离（归一化坐标）"""
        try:
            # 获取坐标轴范围用于归一化
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            x_range = xlim[1] - xlim[0]
            y_range = ylim[1] - ylim[0]
            
            if x_range == 0 or y_range == 0:
                return float('inf')
            
            # 归一化坐标
            norm_click_x = (click_x - xlim[0]) / x_range
            norm_click_y = (click_y - ylim[0]) / y_range
            norm_xdata = (xdata - xlim[0]) / x_range
            norm_ydata = (ydata - ylim[0]) / y_range
            
            # 计算到每个线段的最短距离
            min_distance = float('inf')
            
            for i in range(len(norm_xdata) - 1):
                x1, y1 = norm_xdata[i], norm_ydata[i]
                x2, y2 = norm_xdata[i + 1], norm_ydata[i + 1]
                
                # 计算点到线段的距离
                distance = self.pointToLineSegmentDistance(
                    norm_click_x, norm_click_y, x1, y1, x2, y2)
                min_distance = min(min_distance, distance)
            
            return min_distance
            
        except Exception as e:
            print(f"计算距离时出错: {e}")
            return float('inf')
    
    def pointToLineSegmentDistance(self, px, py, x1, y1, x2, y2):
        """计算点到线段的最短距离"""
        # 线段长度的平方
        segment_length_sq = (x2 - x1) ** 2 + (y2 - y1) ** 2
        
        if segment_length_sq == 0:
            # 线段退化为点
            return np.sqrt((px - x1) ** 2 + (py - y1) ** 2)
        
        # 计算投影参数t
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / segment_length_sq))
        
        # 投影点
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # 返回距离
        return np.sqrt((px - proj_x) ** 2 + (py - proj_y) ** 2)
    
    def highlightSelectedLine(self, selected_line, algorithm_name):
        """高亮选中的线段"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        ax = self.figure.axes[0]
        
        # 重置所有线段的透明度
        for line in ax.lines:
            original_alpha = getattr(line, '_original_alpha', None)
            if original_alpha is None:
                line._original_alpha = line.get_alpha() if line.get_alpha() is not None else 1.0
            line.set_alpha(0.3)  # 设置为半透明
        
        # 高亮选中的线段
        selected_line.set_alpha(selected_line._original_alpha)
        
        # 显示算法信息
        self.showAlgorithmTooltip(algorithm_name, selected_line)
        
        # 重绘画布
        self.canvas.draw_idle()
        
        # 3秒后恢复原始透明度
        QTimer.singleShot(3000, self.restoreLineTransparency)
    
    def restoreLineTransparency(self):
        """恢复线段原始透明度"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        ax = self.figure.axes[0]
        for line in ax.lines:
            original_alpha = getattr(line, '_original_alpha', 1.0)
            line.set_alpha(original_alpha)
        
        self.canvas.draw_idle()
    
    def showAlgorithmTooltip(self, algorithm_name, line):
        """显示算法信息提示"""
        # 获取线段的统计信息
        xdata = line.get_xdata()
        ydata = line.get_ydata()
        
        info_text = f"""
算法: {algorithm_name}

线段属性:
• 颜色: {line.get_color()}
• 线宽: {line.get_linewidth()}
• 线型: {line.get_linestyle()}
• 标记: {line.get_marker()}
• 数据点数: {len(xdata)}
        """.strip()
        
        # 创建信息对话框
        msg = QMessageBox(self)
        msg.setWindowTitle("选中的算法")
        msg.setText(f"您点击了算法: {algorithm_name}")
        msg.setDetailedText(info_text)
        msg.setIcon(QMessageBox.Information)
        
        # 添加"编辑属性"按钮
        edit_button = msg.addButton("编辑属性", QMessageBox.ActionRole)
        msg.addButton("确定", QMessageBox.AcceptRole)
        
        # 显示对话框
        msg.exec_()
        
        # 如果点击了编辑按钮，自动选择该算法
        if msg.clickedButton() == edit_button:
            self.selectAlgorithmInUI(algorithm_name, line)
    
    def selectAlgorithmInUI(self, algorithm_name, line):
        """在UI中选择指定的算法"""
        # 更新算法选择
        self.selectAlgorithm(algorithm_name, algorithm_name)
        
        # 如果有对应的属性控件，滚动到该控件位置
        if hasattr(self, 'all_line_widgets'):
            ax = self.figure.axes[0]
            lines = ax.lines
            
            for i, ax_line in enumerate(lines):
                if ax_line is line and i < len(self.all_line_widgets):
                    widget = self.all_line_widgets[i]
                    if widget.isVisible():
                        # 滚动到该控件
                        scroll_area = widget.parent()
                        while scroll_area and not isinstance(scroll_area, QScrollArea):
                            scroll_area = scroll_area.parent()
                        
                        if scroll_area:
                            scroll_area.ensureWidgetVisible(widget)
                        
                        # 高亮该控件（添加临时边框效果）
                        original_style = widget.styleSheet()
                        widget.setStyleSheet(original_style + """
                            LinePropertiesWidget {
                                border: 3px solid #ff6b6b;
                                background-color: #fff5f5;
                            }
                        """)
                        
                        # 3秒后恢复原始样式
                        QTimer.singleShot(3000, lambda: widget.setStyleSheet(original_style))
                    break
    
    def exportPNG(self):
        """导出为PNG"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PNG图片", last_dir, "PNG Files (*.png);;All Files (*)")
            
            if file_path:
                try:
                    self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                    QMessageBox.information(self, "成功", "PNG导出成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def exportPDF(self):
        """导出为PDF"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PDF文档", last_dir, "PDF Files (*.pdf);;All Files (*)")
            
            if file_path:
                try:
                    # PDF导出使用矢量格式，保持高质量
                    self.figure.savefig(file_path, format='pdf', bbox_inches='tight', 
                                      dpi=300, facecolor='white', edgecolor='none')
                    QMessageBox.information(self, "成功", "PDF导出成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def saveProjectStyle(self):
        """保存工程样式"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可保存的图表")
            return
        
        # 收集当前所有算法的样式
        project_styles = {}
        ax = self.figure.axes[0]
        lines = ax.lines
        
        for i, line in enumerate(lines):
            # 获取算法名称
            algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
            if not algorithm_name:
                algorithm_name = f"Line {i + 1}"
            
            # 获取线条样式
            style = {
                'color': line.get_color(),
                'linewidth': line.get_linewidth(),
                'linestyle': line.get_linestyle(),
                'marker': line.get_marker(),
                'markersize': line.get_markersize(),
                'alpha': line.get_alpha() if line.get_alpha() is not None else 1.0,
                'visible': line.get_visible()
            }
            
            project_styles[algorithm_name] = style
        
        if not project_styles:
            QMessageBox.warning(self, "警告", "没有找到可保存的算法样式")
            return
        
        # 选择保存位置
        last_dir = self.config_manager.get_last_directory()
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存工程样式", last_dir, "JSON Files (*.json);;All Files (*)")
        
        if file_path:
            try:
                # 创建样式文件内容
                import datetime
                style_data = {
                    'version': '1.0',
                    'created_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'description': f'工程样式文件，包含 {len(project_styles)} 个算法的样式设置',
                    'algorithm_count': len(project_styles),
                    'styles': project_styles
                }
                
                # 保存到JSON文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(style_data, f, indent=2, ensure_ascii=False)
                
                # 更新目录位置
                self.config_manager.set_last_directory(str(Path(file_path).parent))
                
                QMessageBox.information(self, "成功", 
                    f"工程样式已保存！\n文件: {Path(file_path).name}\n包含 {len(project_styles)} 个算法样式")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存工程样式失败: {str(e)}")
    
    def loadProjectStyle(self):
        """加载工程样式"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可应用样式的图表")
            return
        
        # 选择样式文件
        last_dir = self.config_manager.get_last_directory()
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开工程样式", last_dir, "JSON Files (*.json);;All Files (*)")
        
        if file_path:
            try:
                # 加载样式文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    style_data = json.load(f)
                
                # 验证文件格式
                if 'styles' not in style_data:
                    QMessageBox.warning(self, "警告", "无效的样式文件格式")
                    return
                
                loaded_styles = style_data['styles']
                
                # 更新目录位置
                self.config_manager.set_last_directory(str(Path(file_path).parent))
                
                # 应用样式到当前图表
                applied_count = self._applyProjectStyles(loaded_styles)
                
                # 重新加载线条属性控件以反映变化
                self.loadLineProperties()
                
                # 重绘画布
                if self.canvas:
                    self.canvas.draw_idle()
                
                # 显示应用结果
                total_loaded = len(loaded_styles)
                ax = self.figure.axes[0]
                total_current = len(ax.lines)
                
                result_msg = f"样式应用完成！\n"
                result_msg += f"样式文件: {Path(file_path).name}\n"
                result_msg += f"加载的样式: {total_loaded} 个\n"
                result_msg += f"当前算法: {total_current} 个\n"
                result_msg += f"成功匹配: {applied_count} 个"
                
                if style_data.get('description'):
                    result_msg += f"\n描述: {style_data['description']}"
                
                QMessageBox.information(self, "样式加载完成", result_msg)
                
            except json.JSONDecodeError:
                QMessageBox.critical(self, "错误", "样式文件格式错误，无法解析JSON")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载工程样式失败: {str(e)}")
    
    def _applyProjectStyles(self, loaded_styles):
        """应用工程样式到当前图表"""
        applied_count = 0
        ax = self.figure.axes[0]
        lines = ax.lines
        
        for i, line in enumerate(lines):
            # 获取当前线条的算法名称
            algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
            if not algorithm_name:
                algorithm_name = f"Line {i + 1}"
            
            # 检查是否有匹配的样式
            if algorithm_name in loaded_styles:
                style = loaded_styles[algorithm_name]
                
                try:
                    # 应用样式
                    if 'color' in style:
                        line.set_color(style['color'])
                    if 'linewidth' in style:
                        line.set_linewidth(style['linewidth'])
                    if 'linestyle' in style:
                        line.set_linestyle(style['linestyle'])
                    if 'marker' in style:
                        line.set_marker(style['marker'])
                    if 'markersize' in style:
                        line.set_markersize(style['markersize'])
                    if 'alpha' in style:
                        line.set_alpha(style['alpha'])
                    if 'visible' in style:
                        line.set_visible(style['visible'])
                    if 'zorder' in style:
                        line.set_zorder(style['zorder'])
                    
                    # 同时保存到配置管理器中
                    if self.config_manager:
                        self.config_manager.save_algorithm_style(algorithm_name, style)
                    
                    applied_count += 1
                    
                except Exception as e:
                    print(f"应用样式失败 {algorithm_name}: {str(e)}")
        
        # 更新图例样式
        self.updateLegendStyles()
        
        return applied_count
    
    def adjustFigureSize(self):
        """自适应调整图表大小"""
        if not self.figure:
            return
        
        try:
            # 获取显示区域的大小
            plot_widget = self.plot_layout.parent()
            if plot_widget:
                # 获取窗口实际大小
                widget_size = plot_widget.size()
                available_width = max(400, widget_size.width() - 50)  # 减去边距，最小400
                available_height = max(300, widget_size.height() - 100)  # 减去工具栏和边距，最小300
                
                # 根据屏幕分辨率动态调整DPI
                screen = QApplication.primaryScreen()
                if screen:
                    screen_dpi = screen.logicalDotsPerInch()
                    # 使用相对合理的DPI值，避免过大或过小
                    dpi = max(80, min(120, screen_dpi * 0.8))
                else:
                    dpi = 100
                
                # 转换为英寸 (matplotlib使用英寸作为单位)
                width_inches = max(4, available_width / dpi)  # 最小4英寸
                height_inches = max(3, available_height / dpi)  # 最小3英寸
                
                # 限制最大尺寸避免过大
                width_inches = min(width_inches, 20)
                height_inches = min(height_inches, 15)
                
                # 检查大小是否真的需要改变
                current_size = self.figure.get_size_inches()
                current_dpi = self.figure.get_dpi()
                
                size_changed = (abs(current_size[0] - width_inches) > 0.1 or 
                              abs(current_size[1] - height_inches) > 0.1 or
                              abs(current_dpi - dpi) > 5)
                
                if size_changed:
                    # 设置图表大小
                    self.figure.set_size_inches(width_inches, height_inches)
                    self.figure.set_dpi(dpi)
                    
                    # 调整子图布局以确保标签可见
                    self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.9)
                    
                    print(f"调整图表大小: {width_inches:.1f}x{height_inches:.1f} 英寸, DPI: {dpi:.0f}, 可用区域: {available_width}x{available_height}")
            else:
                # 如果无法获取父窗口大小，使用默认值
                current_size = self.figure.get_size_inches()
                if abs(current_size[0] - 8) > 0.1 or abs(current_size[1] - 6) > 0.1:
                    self.figure.set_size_inches(8, 6)
                    self.figure.set_dpi(100)
                    print("使用默认图表大小: 8x6 英寸, DPI: 100")
                
        except Exception as e:
            print(f"调整图表大小失败: {e}")
            # 发生错误时使用安全的默认值
            try:
                self.figure.set_size_inches(8, 6)
                self.figure.set_dpi(100)
            except:
                pass
    
    def adjustCanvasSize(self):
        """调整画布大小以适应显示区域"""
        if not self.canvas:
            return
        
        try:
            # 获取父容器大小
            parent = self.canvas.parent()
            if parent:
                # 获取可用空间
                available_size = parent.size()
                toolbar_height = 40 if hasattr(self, 'toolbar') else 0
                
                # 计算画布应该的大小
                canvas_width = max(400, available_size.width() - 20)  # 留出边距，最小400
                canvas_height = max(300, available_size.height() - toolbar_height - 20)  # 最小300
                
                # 检查大小是否真的需要改变
                current_size = self.canvas.size()
                if abs(current_size.width() - canvas_width) < 10 and abs(current_size.height() - canvas_height) < 10:
                    return  # 大小变化太小，不需要调整
                
                # 暂时禁用重绘以避免闪烁
                self.canvas.setUpdatesEnabled(False)
                
                try:
                    # 设置画布的最小和最大大小
                    self.canvas.setMinimumSize(400, 300)
                    self.canvas.resize(canvas_width, canvas_height)
                    
                    # 重新调整图表大小
                    self.adjustFigureSize()
                    
                    # 清理画布缓冲区
                    self.canvas.flush_events()
                    
                finally:
                    # 重新启用更新并强制重绘
                    self.canvas.setUpdatesEnabled(True)
                    self.canvas.draw()
                
                print(f"调整画布大小: {canvas_width}x{canvas_height}")
        except Exception as e:
            print(f"调整画布大小失败: {e}")
            # 确保更新始终被重新启用
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.setUpdatesEnabled(True)
    
    def onCanvasResize(self, event):
        """画布大小改变事件处理"""
        if event and self.figure:
            try:
                # 获取新的画布大小
                width_pixels = event.width
                height_pixels = event.height
                
                if width_pixels > 0 and height_pixels > 0:
                    # 防止过于频繁的调整 - 使用防抖
                    if hasattr(self, '_canvas_resize_timer'):
                        self._canvas_resize_timer.stop()
                    
                    self._canvas_resize_timer = QTimer()
                    self._canvas_resize_timer.setSingleShot(True)
                    self._canvas_resize_timer.timeout.connect(
                        lambda: self._handleCanvasResize(width_pixels, height_pixels))
                    self._canvas_resize_timer.start(100)  # 100ms防抖
                    
            except Exception as e:
                print(f"处理画布大小改变事件失败: {e}")
    
    def _handleCanvasResize(self, width_pixels, height_pixels):
        """处理画布大小调整"""
        try:
            if self.figure and width_pixels > 0 and height_pixels > 0:
                # 转换为英寸
                dpi = self.figure.get_dpi()
                width_inches = width_pixels / dpi
                height_inches = height_pixels / dpi
                
                # 检查大小是否真的需要改变
                current_size = self.figure.get_size_inches()
                if abs(current_size[0] - width_inches) < 0.1 and abs(current_size[1] - height_inches) < 0.1:
                    return  # 大小变化太小，不需要调整
                
                # 更新图表大小
                self.figure.set_size_inches(width_inches, height_inches)
                
                # 强制重绘
                if hasattr(self, 'canvas') and self.canvas:
                    self.canvas.draw()
                
                print(f"画布大小改变: {width_pixels}x{height_pixels} 像素 -> {width_inches:.2f}x{height_inches:.2f} 英寸")
        except Exception as e:
            print(f"处理画布大小调整失败: {e}")
    
    def updateLegend(self):
        """更新图例，只显示可见的算法"""
        if not self.figure or len(self.figure.axes) == 0:
            QMessageBox.warning(self, "警告", "没有可更新的图表")
            return
        
        ax = self.figure.axes[0]
        
        # 收集可见线条和标签
        visible_lines = []
        visible_labels = []
        
        for line in ax.lines:
            if line.get_visible():
                label = line.get_label()
                if label and not label.startswith('_'):
                    visible_lines.append(line)
                    visible_labels.append(label)
        
        # 移除旧图例
        if ax.get_legend():
            ax.get_legend().remove()
        
        # 如果有可见线条且图例启用，则创建新图例
        if visible_lines and self.legend_check.isChecked():
            # 获取当前位置
            location = self.legend_loc_combo.currentText()
            
            # 创建图例
            if location.startswith("outside"):
                if location == "outside right":
                    new_legend = ax.legend(visible_lines, visible_labels, bbox_to_anchor=(1.05, 1), loc='upper left')
                elif location == "outside left":
                    new_legend = ax.legend(visible_lines, visible_labels, bbox_to_anchor=(-0.05, 1), loc='upper right')
                elif location == "outside top":
                    new_legend = ax.legend(visible_lines, visible_labels, bbox_to_anchor=(0.5, 1.05), loc='lower center')
                elif location == "outside bottom":
                    new_legend = ax.legend(visible_lines, visible_labels, bbox_to_anchor=(0.5, -0.05), loc='upper center')
            else:
                new_legend = ax.legend(visible_lines, visible_labels, loc=location)
            
            # 应用字体大小
            for text in new_legend.get_texts():
                text.set_fontsize(self.legend_fontsize_spin.value())
            
            # 应用透明度
            alpha = self.legend_alpha_slider.value() / 100.0
            new_legend.get_frame().set_alpha(alpha)
        
        # 重绘画布
        self.canvas.draw_idle()
        
        # 重新加载算法列表和属性以反映变化
        self.loadAlgorithmList()
        self.loadLineProperties()
        
        QMessageBox.information(self, "成功", f"图例已更新！当前显示 {len(visible_lines)} 个可见算法")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("matplotlib图表编辑器")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = PlotEditorGUI()
    window.show()
    
    # 如果命令行提供了文件路径，自动打开
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if Path(file_path).exists() and file_path.endswith('.pkl'):
            try:
                with open(file_path, 'rb') as f:
                    window.figure = pickle.load(f)
                window.current_file = file_path
                window.setupPlotCanvas()
                window.loadChartProperties()
                window.loadLineProperties()
                window.updateUI()
                print(f"自动加载文件: {file_path}")
            except Exception as e:
                print(f"无法加载文件 {file_path}: {e}")
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 