import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Read the CSV data
df = pd.read_csv('strecha_comparison_results.csv')

# Define colors for each method
colors = {
    'Ours(50)': '#FF6B6B',      # Red/Pink
    'OpenCV(2000)': '#4A4A4A',  # Dark Gray
    'OpenGV(2000)': '#5DADE2',  # Blue
    'PoseLib(20000)': '#9B59B6'        # Purple
}

# Get unique datasets
datasets = df['Dataset'].unique()

# Create figure with two subplots side by side
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8))

# Function to create horizontal bar chart
def create_bar_chart(ax, error_type, title):
    y_pos = np.arange(len(datasets))
    bar_height = 0.2
    
    # Get methods in the order we want them displayed
    methods = ['Ours(50)', 'OpenCV(2000)', 'OpenGV(2000)', 'PoseLib(20000)']
    
    for i, method in enumerate(methods):
        values = []
        for dataset in datasets:
            value = df[(df['Dataset'] == dataset) & (df['Method'] == method)][error_type].values
            if len(value) > 0:
                values.append(value[0])
            else:
                values.append(0)
        
        # Create bars
        color = colors.get(method, '#808080')  # Default gray if method not found
        bars = ax.barh(y_pos + i * bar_height, values, bar_height,
                      label=method, color=color, alpha=0.8)
        
        # Add value labels on bars
        for j, (bar, value) in enumerate(zip(bars, values)):
            if value > 0:
                # Position text at the end of the bar
                ax.text(bar.get_width() + max(values) * 0.01, bar.get_y() + bar.get_height()/2, 
                       f'{value:.2f}', ha='left', va='center', fontsize=9, fontweight='bold')
    
    # Customize the plot
    ax.set_yticks(y_pos + bar_height * 1.5)
    ax.set_yticklabels(datasets)
    ax.set_xlabel(f'{error_type.replace("_", " ")} (degree)')
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.grid(axis='x', alpha=0.3)
    
    # Set x-axis limits to accommodate labels
    max_val = max([df[df['Method'] == method][error_type].max() for method in methods])
    ax.set_xlim(0, max_val * 1.3)
    
    # Invert y-axis to match the original image
    ax.invert_yaxis()

# Create mean error chart
create_bar_chart(ax1, 'Mean_Error', 'Mean error (degree)')

# Create median error chart  
create_bar_chart(ax2, 'Median_Error', 'Median error (degree)')

# Add legend to the right subplot
ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

# Adjust layout
plt.tight_layout()

# Save the plot
plt.savefig('strecha_comparison_plot.png', dpi=300, bbox_inches='tight')
plt.show()

print("Plot saved as 'strecha_comparison_plot.png'")
print("\nData summary:")
print(df.groupby('Method')[['Mean_Error', 'Median_Error']].mean())
