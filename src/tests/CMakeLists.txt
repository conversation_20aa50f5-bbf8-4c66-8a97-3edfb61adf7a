# ==============================================================================
# Copyright (c) 2024 PoSDK Project
# 文件: tests/CMakeLists.txt
# 描述: 测试程序构建配置
# ==============================================================================

# 添加cmake模块路径
list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake")

# ------------------------------------------------------------------------------
# GTest配置
# ------------------------------------------------------------------------------
find_package(GTest REQUIRED)
find_package(OpenGV REQUIRED)
message(STATUS "OpenGV_LIBRARIES: ${OpenGV_LIBRARIES}")
message(STATUS "OpenGV_INCLUDE_DIRS: ${OpenGV_INCLUDE_DIRS}")
# ------------------------------------------------------------------------------
# 测试程序配置
# ------------------------------------------------------------------------------
# 添加测试可执行文件
add_executable(test_all
    experiment_helpers.cpp
    random_generators.cpp
    random_generators.hpp
    time_measurement.cpp
    time_measurement.hpp

    # # ##--------------------------------
    # # ## OpenMVG-based test
    # # ##--------------------------------


    # # # --------------------------------
    # # # OpenCV-based test
    # # # --------------------------------
    # test_method_img2features.cpp
    # test_method_img2matches.cpp
    # test_method_calibrator.cpp

    # # # --------------------------------
    # # # converter test
    # # # --------------------------------
    # test_converter_opengv.cpp
    # test_converter_opencv.cpp
    # test_converter_calibration.cpp
    # test_converter_matches.cpp
    # test_converter_features.cpp
    # test_converter_openmvg.cpp
    # # # --------------------------------
    # # # DataIO/Protocol test
    # # # --------------------------------
    # test_data_relative_poses.cpp
    # test_data_global_poses.cpp
    # test_data_sample.cpp

    # # # --------------------------------
    # # # proto test
    # # # --------------------------------
    # test_proto_tracks.cpp
    # test_proto_features.cpp
    # test_proto_matches.cpp
    # test_proto_camera_model.cpp

    # # #--------------------------------
    # # # opengv test
    # # #--------------------------------
    # test_opengv_model_estimator.cpp
    # test_data_matches_copy.cpp
    # # #--------------------------------
    # # # ransac test
    # # #--------------------------------


    # # #--------------------------------
    # # # g2o test
    # # #--------------------------------
    # test_g2o_io.cpp

    # # #--------------------------------
    # # # Pose-only series test
    # # --------------------------------
    # test_lirp_method.cpp
    # test_method_LiGT.cpp
    # test_method_PA.cpp

    ##--------------------------------
    ## Dataset
    ##--------------------------------
    test_Strecha.cpp
    # test_Strecha_colmap.cpp


    # # ##--------------------------------
    # # ## simulator test
    # # ##--------------------------------
    # test_simu_two_view_estimator.cpp
    # test_barath_two_view_estimator.cpp
    # test_executable_path.cpp


    # # ##--------------------------------
    # # ## rotation averaging test
    # # ##--------------------------------
    # test_rotation_averaging.cpp
    # test_compute_similarity.cpp



)

# 设置输出目录
set_target_properties(test_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# 添加依赖库
target_link_libraries(test_all
    PRIVATE
        PoEngine::posdk  # 链接到聚合库
        GTest::GTest
        GTest::Main
        ${OpenGV_LIBRARIES}
        Eigen3::Eigen
)

# 明确指定链接库目录
target_link_directories(test_all PRIVATE ${OUTPUT_LIB_DIR})

# 添加包含目录
target_include_directories(test_all
    PRIVATE
        ${OUTPUT_INCLUDE_DIR}     # 这里包含了生成的头文件目录
        ${CMAKE_SOURCE_DIR}/src   # 添加源码目录
        ${OpenGV_INCLUDE_DIRS}    # 添加OpenGV的头文件目录
)


target_compile_definitions(test_all
    PRIVATE
        PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}"
)

# ------------------------------------------------------------------------------
# 调试信息
# ------------------------------------------------------------------------------
# 打印包含目录信息
get_target_property(include_dirs test_all INCLUDE_DIRECTORIES)
message(STATUS "test_all include directories:")
foreach(dir ${include_dirs})
    message(STATUS "  ${dir}")
endforeach()

# 验证文件存在
message(STATUS "Checking po_core.hpp location:")
message(STATUS "  Expected at: ${OUTPUT_INCLUDE_DIR}/po_core.hpp")
if(EXISTS "${OUTPUT_INCLUDE_DIR}/po_core.hpp")
    message(STATUS "  File exists")
else()
    message(WARNING "  File does not exist")
endif()

# 验证OpenGV头文件存在
message(STATUS "Checking OpenGV headers location:")
message(STATUS "  Expected at: ${OpenGV_INCLUDE_DIRS}/opengv/types.hpp")
if(EXISTS "${OpenGV_INCLUDE_DIRS}/opengv/types.hpp")
    message(STATUS "  OpenGV headers found")
else()
    message(WARNING "  OpenGV headers not found")
endif()

# 验证converter头文件存在
message(STATUS "Checking converter headers location:")
message(STATUS "  Expected at: ${OUTPUT_INCLUDE_DIR}/common/converter/converter_opengv.hpp")
if(EXISTS "${OUTPUT_INCLUDE_DIR}/common/converter/converter_opengv.hpp")
    message(STATUS "  Converter headers found")
else()
    message(WARNING "  Converter headers not found")
endif()

# ------------------------------------------------------------------------------
# 添加到CTest
# ------------------------------------------------------------------------------
add_test(
    NAME test_all
    COMMAND test_all
    WORKING_DIRECTORY ${OUTPUT_BIN_DIR}
)
