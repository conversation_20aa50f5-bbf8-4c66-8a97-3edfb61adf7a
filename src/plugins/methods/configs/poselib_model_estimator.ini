[poselib_model_estimator]
# 基本配置
ProfileCommit=PoseLib相对位姿估计参数配置

log_level=0

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 视图对配置
view_i=0  # 源视图ID
view_j=1  # 目标视图ID

# 算法选择
# 
# 直接方法 (Direct Methods):
# - relpose_5pt: 5点相对位姿算法 (推荐，默认)
# - relpose_7pt: 7点相对位姿算法（返回基础矩阵）
# - relpose_8pt: 8点相对位姿算法
# - relpose_upright_3pt: 直立3点相对位姿算法
# - relpose_upright_planar_3pt: 直立平面3点相对位姿算法
#
# RANSAC方法 (Robust Methods):
# - relpose_5pt_ransac: 5点相对位姿RANSAC ⭐ (推荐)
# - relpose_7pt_ransac: 7点相对位姿RANSAC
# - relpose_8pt_ransac: 8点相对位姿RANSAC
# - relpose_upright_3pt_ransac: 直立3点相对位姿RANSAC
#
# 注意：算法名称中包含"_ransac"的会自动使用RANSAC鲁棒估计
algorithm=relpose_5pt_ransac

# 模型优化配置
# 可选优化方法:
# - none: 不进行优化 (默认)
# - nonlinear: 使用非线性优化方法
refine_model=nonlinear

# RANSAC参数配置 (仅在算法名称包含"_ransac"时生效)
ransac_threshold=1              # RANSAC阈值 (重投影误差阈值)
ransac_max_iterations=20000         # 最大迭代次数
progressive_sampling=false          # 是否使用渐进采样

# 注意: 质量控制参数已移至TwoViewEstimator统一管控
# 具体参数请在two_view_estimator.ini中配置:
# - enable_quality_validation: 是否启用质量验证
# - min_geometric_inliers: 最小几何内点数量 
# - min_inlier_ratio: 最小内点比例
# 这样可以为所有估计器提供统一的质量验证标准

[refine]
# 模型优化参数配置 (仅在refine_model!=none时生效)
max_iterations=100             # 优化最大迭代次数
loss_scale=1.0                 # 损失函数尺度参数 