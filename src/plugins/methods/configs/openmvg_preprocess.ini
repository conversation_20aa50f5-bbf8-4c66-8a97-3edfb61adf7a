[openmvg_preprocess]

# 性能分析说明
ProfileCommit=OpenMVG预处理插件配置

# 性能分析选项
enable_profiling=true    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估

# ======================================================
# 通用设置
# ======================================================
# 输入图像文件夹
; images_folder = {root_dir}/castle-P19/images
; images_folder = {root_dir}/castle-P30/images
; images_folder = {root_dir}/entry-P10/images
; images_folder = {root_dir}/fountain-P11/images
; images_folder = {root_dir}/Herz-Jesus-P8/images
images_folder = {root_dir}/Herz-Jesus-P25/images


# OpenMVG二进制文件目录 - 自动检测
# 系统会自动检测以下路径：
# - ../../dependencies/openMVG/build/Darwin-arm64-Release  (macOS Apple Silicon)
# - ../../dependencies/openMVG/build/Darwin-x86_64-Release (macOS Intel)
# - ../../dependencies/openMVG/build/Linux-x86_64-Release  (Linux)
# - 系统PATH中的安装位置


# 工作目录 (插件运行时创建的临时目录)
work_dir = {exe_dir}/openmvg_strecha_test_work

# 是否在运行前清空工作目录
# true: 每次运行前删除并重新创建work_dir目录（确保干净的运行环境）
# false: 保留工作目录中的现有文件（可用于增量处理或调试）
is_reclear_workdir = true

# 是否强制重新计算所有步骤（不使用已存在的缓存文件）
force_compute = false

# 是否输出详细的调试日志 (如果为true，插件会保留work_dir供检查)
debug_output = true

# 是否转换推测性匹配数据 (putative matches) 而不是几何过滤后的匹配数据
# 如果为 true, 使用由 ComputeMatches 直接生成的匹配文件 (通常是 matches.putative.bin)
# 如果为 false, 使用由 GeometricFilter 生成的匹配文件 (通常是 matches.f.bin 或类似)
convert_putative_data = true

# ======================================================
# 注意：管道步骤说明
# ======================================================
# 基础步骤（总是执行）：
# 1. SfMInit_ImageListing - 创建SfM数据文件
# 2. ComputeFeatures - 提取图像特征
# 3. PairGenerator - 生成图像对列表（可选，在ComputeMatches之前）
# 4. ComputeMatches - 计算特征匹配（可使用PairGenerator生成的pairs文件）
# 5. GeometricFilter - 几何过滤匹配
#
# 可选步骤（通过相应的enable_xxx参数控制）：
# 6. SfM - 三维重建（支持多种引擎：INCREMENTAL/GLOBAL/STELLAR）
# 7. ComputeSfM_DataColor - 为重建点云着色
# 8. EvalQuality - 质量评估与真值对比（需要提供gt_dataset_path）
#
# 输出数据：
# - data_images: 图像数据和相机参数
# - data_features: 图像特征点和描述子
# - data_matches: 特征匹配对
# 如果启用了SfM重建，重建结果会保存在work_dir/reconstruction_dir中

# ======================================================
# Intermediate File Paths (relative to work_dir)
# These options control the names and locations of intermediate files
# generated by the OpenMVG tools within the working directory.
# ======================================================

# SfMInit_ImageListing: Output directory for sfm_data.json and feature/match files, relative to work_dir.
# This directory will store sfm_data.json, .feat files, .desc files (if generated), and match files.
sfm_out_dir = matches

# SfMInit_ImageListing: Filename for the SfM data file, within intermediate_files_dir.
sfm_data_file = sfm_data.json

# ComputeMatches: Output filename for putative matches, relative to intermediate_files_dir.
# OpenMVG will infer format (binary/text) from extension (.bin or .txt).
putative_matches = matches.putative.bin

# GeometricFilter: Input putative matches filename, relative to intermediate_files_dir.
# Should typically match the output of ComputeMatches (i.e., the value of putative_matches_filename).
# If left empty, it will default to the value of 'putative_matches_filename'.
geom_filter_in = 

# GeometricFilter: Output filename template for geometrically filtered matches, relative to intermediate_files_dir.
# Use {GEOM_MODEL} as a placeholder for the geometric model (f, e, or h).
# Example: matches.{GEOM_MODEL}.bin or filtered_matches_{GEOM_MODEL}.txt
# OpenMVG will infer format (binary/text) from extension (.bin or .txt).
geom_filter_out_tpl = matches.{GEOM_MODEL}.bin

# ======================================================
# Original OpenMVG settings below
# ======================================================

# 用于OpenMVG某些并行步骤的线程数 (0表示自动选择，通常是最大可用线程数)
num_threads = 0

# 用于特征描述子缓存的最大内存（MB），0表示无限制 (OpenMVG ComputeFeatures/ComputeMatches/GeometricFilter)
cache_size = 0

# 是否保存特征和匹配到文件
save_features = true
save_matches = true

# 保存特征和匹配的路径
features_save_path = storage/features/features_all
matches_save_path = storage/matches/matches_all

# ======================================================
# SfMInit_ImageListing 设置 (openMVG_main_SfMInit_ImageListing)
# ======================================================
# 相机传感器宽度数据库文件路径。如果留空，OpenMVG将不使用数据库。
# OpenMVG默认安装路径通常是 /usr/local/share/openMVG/sensor_width_camera_database.txt
camera_sensor_db = 

# 相机模型类型 
# 1: 针孔相机
# 2: 针孔带1个径向畸变参数 
# 3: 针孔带3个径向畸变参数
# 4: 带切向畸变的Brown模型
camera_model = 1

# 相机内参矩阵，格式: "fx;0;cx;0;fy;cy;0;0;1"
# 如果没有指定，将尝试从EXIF或camera_sensor_db中获取
intrinsics = 

# 像素焦距，仅当无法从EXIF或内参获取时使用
# 默认为-1，这意味着OpenMVG将尝试从EXIF信息估计
focal_pixels = -1.0

# 是否将所有图像视为共享相同的内参
# 0: 每张图片使用单独的内参
# 1: 所有图片共享内参
group_camera_model = 1

# 是否使用先验位姿（如使用EXIF信息）
use_pose_prior = false

# 先验位姿的权重，格式: "旋转权重;GPS位置权重;GPS高度权重"
prior_weights = 1.0;1.0;1.0

# GPS到XYZ转换方法
# 0: 从ECEF (WGS84) 到 XYZ 的转换
# 1: 从UTM (WGS84) 到 XYZ 的转换
gps_to_xyz_method = 0

# ======================================================
# ComputeFeatures 设置 (openMVG_main_ComputeFeatures)
# ======================================================
# 特征描述子方法
# SIFT, AKAZE, AKAZE_FLOAT, AKAZE_MLDB
describer_method = SIFT

# 特征描述子的预设质量等级
# LOW, MEDIUM, NORMAL, HIGH, ULTRA
describer_preset = NORMAL

# 是否使用直立描述子（忽略方向信息）
upright = false

# ======================================================
# ComputeMatches 设置 (openMVG_main_ComputeMatches)
# ======================================================
# 特征匹配方法
# BRUTEFORCEL2: L2距离的暴力匹配
# ANNL2: 近似最近邻L2距离
# CASCADEHASHINGL2: 级联哈希L2距离
# FASTCASCADEHASHINGL2: 快速级联哈希L2距离
# BRUTEFORCEHAMMING: 汉明距离的暴力匹配
nearest_matching_method = FASTCASCADEHASHINGL2

# Lowe 比率测试阈值 (0.8是常用值)
ratio = 0.8

# 指定图像对的列表文件路径 (如果为空，则对所有图像对进行匹配)
pair_list = 

# 是否使用预先筛选方法（使用少量特征快速过滤图像对）
use_preemptive = false

# 预先筛选使用的特征数量
preemptive_feature_count = 200

# ======================================================
# GeometricFilter 设置 (openMVG_main_GeometricFilter)
# ======================================================
# 几何模型过滤类型
# f: 基础矩阵 (Fundamental Matrix)
# e: 本质矩阵 (Essential Matrix)
# h: 单应矩阵 (Homography Matrix)
geometric_model = f

# 输入图像对列表文件路径 (如果为空，则处理所有匹配的图像对)
input_pairs = 

# 输出图像对列表文件路径 (如果为空，则不保存)
output_pairs = 

# 是否使用引导匹配进行精炼
guided_matching = false

# RANSAC最大迭代次数
max_iteration = 2048 

# ======================================================
# PairGenerator 设置 (openMVG_main_PairGenerator) - 可选步骤
# ======================================================
# 是否启用PairGenerator步骤（用于生成图像对列表）
enable_pair_generator = true

# 输出pairs文件名，相对于intermediate_files_dir
pairs_file = pairs.bin

# 图像对生成模式
# EXHAUSTIVE: 穷举所有图像对
# CONTIGUOUS: 连续图像对
# VOCABULARY_TREE: 基于词汇树的图像对选择
pair_mode = 

# 连续图像对数量（当pair_mode为CONTIGUOUS时使用）
contiguous_count = 

# ======================================================
# SfM 重建设置 (openMVG_main_SfM) - 可选步骤
# ======================================================
# 是否启用SfM重建步骤
enable_sfm_reconstruction = true

# SfM重建输出目录名（相对于work_dir）
reconstruction_dir = reconstruction_global

# SfM引擎类型
# INCREMENTAL: 增量式重建
# INCREMENTALV2: 增量式重建V2（实验性）
# GLOBAL: 全局重建
# STELLAR: 星状重建
sfm_engine = GLOBAL

# PoSDK tracks 文件导出路径
# 如果指定了路径，OpenMVG SfM 将导出 tracks 文件到指定位置
# 如果为空字符串，则不导出 tracks 文件
# 例如：export_tracks_file = storage/tracks/tracks_all.tracks
# /Users/<USER>/Documents/PoMVG/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/output/bin/tracks.track
export_tracks_file = 

# PoSDK relative poses 文件导出路径
# 如果指定了路径，OpenMVG SfM 将导出 relative poses 文件到指定位置
# 如果为空字符串，则不导出 relative poses 文件
# 例如：export_relative_poses_file = storage/poses/relative_poses.g2o
# export_relative_poses_file = /Users/<USER>/Documents/PoMVG/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/output/bin/relative_poses.g2o
export_relative_poses_file = 
# ======================================================
# Bundle Adjustment 设置
# ======================================================
# 内参精炼配置
# ADJUST_ALL: 精炼所有现有参数（默认）
# NONE: 内参保持不变
# ADJUST_FOCAL_LENGTH: 仅精炼焦距
# ADJUST_PRINCIPAL_POINT: 仅精炼主点位置
# ADJUST_DISTORTION: 仅精炼畸变系数
# 可以用'|'组合，如：ADJUST_FOCAL_LENGTH|ADJUST_PRINCIPAL_POINT
refine_intrinsic_config = NONE

# 外参精炼配置
# ADJUST_ALL: 精炼所有现有参数（默认）
# NONE: 外参保持不变
refine_extrinsic_config = ADJUST_ALL

# 是否使用运动先验信息（如GPS位置）
use_motion_priors = false

# ======================================================
# Incremental SfM 设置（当sfm_engine为INCREMENTAL时）
# ======================================================
# 三角化方法
# 0: DIRECT_LINEAR_TRANSFORM
# 1: L1_ANGULAR
# 2: LINFINITY_ANGULAR
# 3: INVERSE_DEPTH_WEIGHTED_MIDPOINT
triangulation_method = 

# 相机位姿估计方法
# 0: DLT_6POINTS
# 1: P3P_KE_CVPR17
# 2: P3P_KNEIP_CVPR11
# 3: P3P_NORDBERG_ECCV18
# 4: P3P_DING_CVPR23
# 5: UP2P_KUKELOVA_ACCV10
resection_method = 

# SfM相机模型类型（针对未知内参的视图）
# 1: Pinhole
# 2: Pinhole radial 1
# 3: Pinhole radial 3（默认）
# 4: Pinhole radial 3 + tangential 2
# 5: Pinhole fisheye
sfm_camera_model = 3

# 初始图像对（仅用于INCREMENTAL）
# 第一张图像的文件名（不含路径）
initial_pair_a = 

# 第二张图像的文件名（不含路径）
initial_pair_b = 

# ======================================================
# Incremental SfM V2 设置（当sfm_engine为INCREMENTALV2时）
# ======================================================
# SfM初始化方法
# EXISTING_POSE: 从现有的sfm_data相机位姿初始化重建
# MAX_PAIR: 从匹配数最多的图像对初始化重建
# AUTO_PAIR: 自动选择图像对初始化重建
# STELLAR: 使用星状重建初始化
sfm_initializer = STELLAR

# ======================================================
# Global SfM 设置（当sfm_engine为GLOBAL时）
# ======================================================
# 旋转平均方法
# 1: L1最小化
# 2: L2最小化（默认）
rotation_averaging = 2

# 平移平均方法
# 1: L1最小化
# 2: L2最小化的弦距平方和
# 3: SoftL1最小化（默认）
# 4: LiGT: 来自旋转和匹配的线性全局平移约束
translation_averaging = 3

# ======================================================
# Stellar SfM 设置（当sfm_engine为STELLAR时）
# ======================================================
# 图简化方法
# NONE: 无简化
# MST_X: 最小生成树简化
# STAR_X: 星形简化
graph_simplification = MST_X

# 图简化值（必须 > 1）
graph_simplification_value = 5

# ======================================================
# ComputeSfM_DataColor 设置 (openMVG_main_ComputeSfM_DataColor) - 可选步骤
# ======================================================
# 是否启用点云着色步骤
enable_point_cloud_coloring = false

# 着色点云文件名
colored_ply_file = colorized.ply

# ======================================================
# EvalQuality 设置 (openMVG_main_evalQuality) - 可选步骤
# ======================================================
# 是否启用质量评估步骤（与真值数据集对比）
enable_quality_evaluation = true

# 真值数据集路径
# 对于Strecha数据集，可以是：
# 1. 包含gt_dense_cameras文件夹的目录路径（如：/path/to/castle-P19）
# 2. 直接指向真值sfm_data文件的路径（如：/path/to/gt_sfm_data.bin）
gt_dataset_path = {images_folder}/..

# 质量评估结果输出目录名（相对于work_dir）
eval_output_dir = quality_evaluation

# ======================================================
# 使用示例配置
# ======================================================
# 
# 示例1：仅预处理（默认配置）
# enable_pair_generator = false
# enable_sfm_reconstruction = false
# enable_point_cloud_coloring = false
# 输出：data_images, data_features, data_matches
#
# 示例2：完整的Global SfM管道
# enable_pair_generator = false  # 通常Global SfM不需要pairs文件
# enable_sfm_reconstruction = true
# enable_point_cloud_coloring = true
# sfm_engine = GLOBAL
# 输出：data_images, data_features, data_matches + 重建结果在work_dir中
#
# 示例3：增量式SfM管道  
# enable_pair_generator = true   # 增量式可能需要pairs文件进行优化
# enable_sfm_reconstruction = true
# enable_point_cloud_coloring = true
# sfm_engine = INCREMENTAL
# initial_pair_a = img001.jpg
# initial_pair_b = img002.jpg
#
# 示例4：高质量重建配置
# describer_preset = HIGH         # 高质量特征提取
# ratio = 0.7                     # 更严格的匹配阈值
# sfm_engine = GLOBAL
# refine_intrinsic_config = ADJUST_ALL
# refine_extrinsic_config = ADJUST_ALL 

# 示例5：导出 tracks 文件配置
# enable_sfm_reconstruction = true
# sfm_engine = GLOBAL
# export_tracks_file = storage/tracks/tracks_all.tracks  # 导出 tracks 到指定路径
# 输出：data_images, data_features, data_matches + 重建结果 + tracks文件

# 示例6：Strecha数据集评估配置
# enable_sfm_reconstruction = true
# enable_point_cloud_coloring = true
# enable_quality_evaluation = true
# sfm_engine = GLOBAL
# gt_dataset_path = /path/to/strecha/castle-P19  # Strecha数据集路径
# 输出：重建结果 + 与真值的对比评估报告（HTML和JSON格式）
#
# 说明：Strecha数据集评估会生成以下文件：
# - work_dir/quality_evaluation/ExternalCalib_Report.html（详细的HTML评估报告）
# - work_dir/quality_evaluation/gt_eval_stats_blob.json（JSON格式的统计数据）
# - work_dir/quality_evaluation/camGT.ply（真值相机位置可视化）
# - work_dir/quality_evaluation/camComputed.ply（重建相机位置可视化）

# 示例7：导出 relative poses 文件配置
# enable_sfm_reconstruction = true
# sfm_engine = GLOBAL
# export_relative_poses_file = storage/poses/relative_poses.g2o  # 导出 relative poses 到指定路径
# 输出：data_images, data_features, data_matches + 重建结果 + relative poses文件

# 示例8：同时导出 tracks 和 relative poses
# enable_sfm_reconstruction = true
# sfm_engine = GLOBAL
# export_tracks_file = storage/tracks/tracks_all.tracks
# export_relative_poses_file = storage/poses/relative_poses.g2o
# 输出：完整的数据包 + tracks文件 + relative poses文件
#
# 说明：relative poses 导出功能会生成以下文件：
# - storage/poses/relative_poses.g2o（G2O格式的相对位姿文件）
# - 包含从 OpenMVG Global SfM 计算得到的相对旋转信息
# - 平移部分设置为零向量，仅包含旋转信息
# - 可以通过 PoSDK 的 LoadFromG2O 函数加载使用

# 示例9：清空工作目录的配置
# is_reclear_workdir = true    # 每次运行前清空工作目录，确保干净环境
# work_dir = /path/to/work     # 工作目录路径
# 说明：设置为true时，每次运行前会删除work_dir目录中的所有内容
#       这在进行重复实验或确保输出一致性时很有用
#       设置为false时，会保留已有文件，可用于增量处理或调试 