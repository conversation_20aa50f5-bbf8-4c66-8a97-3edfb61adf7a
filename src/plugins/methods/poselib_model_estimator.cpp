/**
 * @file poselib_model_estimator.cpp
 * @brief PoseLib相对位姿估计器实现
 * @copyright Copyright (c) 2024 XXX公司
 */

#include "poselib_model_estimator.hpp"
#include <limits>
#include <cmath>

namespace PluginMethods
{

    PoseLibModelEstimator::PoseLibModelEstimator()
    {
        // 注册所需数据类型
        required_package_["data_sample"] = nullptr; // DataSample<IdMatches>
        required_package_["data_features"] = nullptr;
        required_package_["data_camera_models"] = nullptr;

        // 初始化默认配置路径
        InitializeDefaultConfigPath();

        // 加载refine配置
        InitializeDefaultConfigPath("refine");
    }

    DataPtr PoseLibModelEstimator::Run()
    {
        DisplayConfigInfo();

        // 获取算法参数
        std::string algorithm = GetOptionAsString("algorithm", "relpose_5pt");

        // 1. 获取输入数据
        auto sample_ptr = CastToSample<IdMatches>(required_package_["data_sample"]);
        auto features_ptr = GetDataPtr<FeaturesInfo>(required_package_["data_features"]);
        auto cameras_ptr = GetDataPtr<CameraModels>(required_package_["data_camera_models"]);

        if (!sample_ptr || !features_ptr || !cameras_ptr)
        {
            PO_LOG_ERR << "Invalid input data" << std::endl;
            return nullptr;
        }

        // 2. 从method_options_获取视图对信息
        ViewPair view_pair(
            GetOptionAsIndexT("view_i", 0), // 源视图ID
            GetOptionAsIndexT("view_j", 1)  // 目标视图ID
        );

        // 3. 获取匹配数据并进行初步检查
        if (sample_ptr->empty())
        {
            PO_LOG_ERR << "Empty sample data" << std::endl;
            return nullptr;
        }

        // 检查匹配点对数量是否满足最低要求
        size_t total_matches = sample_ptr->size();
        size_t min_samples = GetMinimumSamplesForAlgorithm(algorithm);

        if (total_matches < min_samples)
        {
            PO_LOG_ERR << "Insufficient matches for algorithm " << algorithm
                       << ": got " << total_matches << ", need at least " << min_samples << std::endl;

            // 将所有匹配标记为外点
            for (auto &match : *sample_ptr)
            {
                match.is_inlier = false;
            }
            return nullptr;
        }

        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "Algorithm: " << algorithm
                                  << ", Total matches: " << total_matches
                                  << ", Min required: " << min_samples << std::endl;
        }

        // 4. 转换为PoseLib格式的2D点对和相机参数
        std::vector<poselib::Point2D> points1, points2;
        poselib::Camera camera1, camera2;
        if (!ConvertToPoseLibPoints(sample_ptr, features_ptr, cameras_ptr, view_pair, points1, points2, camera1, camera2))
        {
            PO_LOG_ERR << "Failed to convert matches to PoseLib format" << std::endl;
            return nullptr;
        }

        // 5. 估计相对位姿
        poselib::CameraPose best_pose;
        std::vector<char> inliers;

        // 记录核心计算开始时间
        auto core_start_time = std::chrono::high_resolution_clock::now();

        if (IsRansacAlgorithm(algorithm))
        {
            // RANSAC方法
            best_pose = EstimateRelativePoseRansac(points1, points2, camera1, camera2, inliers);

            // 更新内点/外点信息到IdMatches中
            if (sample_ptr && !sample_ptr->empty())
            {
                // 先将所有匹配点标记为外点
                for (auto &match : *sample_ptr)
                {
                    match.is_inlier = false;
                }

                // 根据RANSAC内点结果更新
                for (size_t i = 0; i < inliers.size() && i < sample_ptr->size(); ++i)
                {
                    if (inliers[i])
                    {
                        (*sample_ptr)[i].is_inlier = true;
                    }
                }

                if (log_level_ >= PO_LOG_NORMAL)
                {
                    size_t num_inliers = std::count(inliers.begin(), inliers.end(), true);
                    PO_LOG(PO_LOG_NORMAL) << "RANSAC algorithm: " << algorithm << std::endl;
                    PO_LOG(PO_LOG_NORMAL) << "Total matches: " << sample_ptr->size()
                                          << ", Inliers: " << num_inliers
                                          << " (" << (100.0 * num_inliers / sample_ptr->size()) << "%)" << std::endl;
                }
            }
        }
        else
        {
            // 直接方法
            poselib::CameraPoseVector poses = EstimateRelativePose(points1, points2, camera1, camera2);
            if (poses.empty())
            {
                PO_LOG_ERR << "No valid poses found" << std::endl;
                return nullptr;
            }
            best_pose = poses[0];

            // 对于直接方法，所有匹配点都被认为是内点
            if (sample_ptr && !sample_ptr->empty())
            {
                // 将所有匹配点标记为内点
                for (auto &match : *sample_ptr)
                {
                    match.is_inlier = true;
                }

                if (log_level_ >= PO_LOG_NORMAL)
                {
                    PO_LOG(PO_LOG_NORMAL) << "Direct algorithm: " << algorithm << std::endl;
                    PO_LOG(PO_LOG_NORMAL) << "Total matches: " << sample_ptr->size()
                                          << ", All marked as inliers (100%)" << std::endl;
                }
            }
        }

        // 6. 检查位姿的有效性
        if (!IsPoseValid(best_pose))
        {
            PO_LOG_ERR << "Invalid pose estimated" << std::endl;
            return nullptr;
        }

        // 7. 检查是否需要进行模型优化
        std::string refine_model_str = GetOptionAsString("refine_model", "none");
        RefineMethod refine_method = CreateRefineMethodFromString(refine_model_str);

        if (refine_method != RefineMethod::NONE)
        {
            if (log_level_ >= PO_LOG_NORMAL)
            {
                PO_LOG(PO_LOG_NORMAL) << "开始模型优化，方法: " << refine_model_str << std::endl;
            }

            best_pose = RefineModel(points1, points2, camera1, camera2, best_pose, refine_method);

            if (log_level_ >= PO_LOG_NORMAL)
            {
                PO_LOG(PO_LOG_NORMAL) << "模型优化完成" << std::endl;
            }
        }

        // 记录核心计算结束时间
        auto core_end_time = std::chrono::high_resolution_clock::now();
        double core_time_ms = std::chrono::duration<double, std::milli>(core_end_time - core_start_time).count();

        // 设置核心计算时间（在日志输出之前，确保时间测量准确性）
        SetCoreTime(core_time_ms);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "PoseLib core computation time: " << core_time_ms << " ms" << std::endl;
        }

        // 8. 转换为PoSDK格式并返回结果
        RelativePose relative_pose = ConvertPoseLibToOpenGV(best_pose, view_pair.first, view_pair.second);

        return std::make_shared<DataMap<RelativePose>>(relative_pose);
    }

    poselib::CameraPoseVector PoseLibModelEstimator::EstimateRelativePose(
        const std::vector<poselib::Point2D> &points1,
        const std::vector<poselib::Point2D> &points2,
        const poselib::Camera &camera1,
        const poselib::Camera &camera2)
    {
        std::string algorithm = GetOptionAsString("algorithm", "relpose_5pt");
        poselib::CameraPoseVector poses;

        try
        {
            // 将2D点转换为bearing vectors（直接算法需要）
            std::vector<Eigen::Vector3d> x1, x2;
            x1.reserve(points1.size());
            x2.reserve(points2.size());

            for (size_t i = 0; i < points1.size(); ++i)
            {
                // 通过相机内参将像素坐标转换为归一化坐标，然后构造单位向量
                double x_norm1 = (points1[i](0) - camera1.params[2]) / camera1.params[0];
                double y_norm1 = (points1[i](1) - camera1.params[3]) / camera1.params[1];
                double x_norm2 = (points2[i](0) - camera2.params[2]) / camera2.params[0];
                double y_norm2 = (points2[i](1) - camera2.params[3]) / camera2.params[1];

                Eigen::Vector3d bearing1(x_norm1, y_norm1, 1.0);
                Eigen::Vector3d bearing2(x_norm2, y_norm2, 1.0);
                bearing1.normalize();
                bearing2.normalize();

                x1.push_back(bearing1);
                x2.push_back(bearing2);
            }

            if (algorithm == "relpose_5pt")
            {
                // 5点相对位姿算法
                poselib::relpose_5pt(x1, x2, &poses);
            }
            else if (algorithm == "relpose_7pt")
            {
                // 7点基础矩阵算法转相对位姿
                // 注意：PoseLib可能没有直接的7点相对位姿算法，但我们可以通过基础矩阵来实现
                if (x1.size() >= 7 && x2.size() >= 7)
                {
                    // 对于7点算法，我们使用前7个点
                    std::vector<Eigen::Vector3d> x1_7(x1.begin(), x1.begin() + 7);
                    std::vector<Eigen::Vector3d> x2_7(x2.begin(), x2.begin() + 7);

                    try
                    {
                        // 使用5点算法作为fallback，因为PoseLib的7点算法可能需要特殊处理
                        poselib::relpose_5pt(x1, x2, &poses);

                        if (log_level_ >= PO_LOG_VERBOSE)
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "Using 5pt algorithm as fallback for 7pt" << std::endl;
                        }
                    }
                    catch (const std::exception &e)
                    {
                        PO_LOG_ERR << "Error in 7pt algorithm fallback: " << e.what() << std::endl;
                    }
                }
                else
                {
                    PO_LOG_ERR << "Insufficient points for 7pt algorithm (need at least 7, got "
                               << x1.size() << ")" << std::endl;
                }
            }
            else if (algorithm == "relpose_8pt")
            {
                // 8点相对位姿算法
                poselib::relpose_8pt(x1, x2, &poses);
            }
            else if (algorithm == "relpose_upright_3pt")
            {
                // 直立3点相对位姿算法
                poselib::relpose_upright_3pt(x1, x2, &poses);
            }
            else if (algorithm == "relpose_upright_planar_3pt")
            {
                // 直立平面3点相对位姿算法
                poselib::relpose_upright_planar_3pt(x1, x2, &poses);
            }
            else
            {
                PO_LOG_ERR << "Unknown algorithm: " << algorithm << std::endl;
                PO_LOG(PO_LOG_NONE) << "Using default algorithm: relpose_5pt" << std::endl;
                poselib::relpose_5pt(x1, x2, &poses);
            }
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in EstimateRelativePose: " << e.what() << std::endl;
        }

        return poses;
    }

    poselib::CameraPose PoseLibModelEstimator::EstimateRelativePoseRansac(
        const std::vector<poselib::Point2D> &points1,
        const std::vector<poselib::Point2D> &points2,
        const poselib::Camera &camera1,
        const poselib::Camera &camera2,
        std::vector<char> &inliers)
    {
        std::string algorithm = GetOptionAsString("algorithm", "relpose_5pt_ransac");
        poselib::CameraPose best_pose;

        // 创建RANSAC配置
        poselib::RansacOptions ransac_opt;
        ransac_opt.max_iterations = GetOptionAsIndexT("ransac_max_iterations", 1000);
        ransac_opt.max_epipolar_error = GetOptionAsFloat("ransac_threshold", 1e-4);
        ransac_opt.progressive_sampling = GetOptionAsBool("progressive_sampling", true);

        // 创建Bundle配置
        poselib::BundleOptions bundle_opt;
        bundle_opt.max_iterations = 100;

        try
        {
            // 使用转换后的2D points进行RANSAC估计
            poselib::RansacStats stats = poselib::estimate_relative_pose(
                points1, points2, camera1, camera2, ransac_opt, bundle_opt, &best_pose, &inliers);

            if (log_level_ >= PO_LOG_VERBOSE)
            {
                size_t num_inliers = std::count(inliers.begin(), inliers.end(), true);
                PO_LOG(PO_LOG_VERBOSE) << "RANSAC iterations: " << stats.iterations
                                       << ", Inliers: " << num_inliers << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in EstimateRelativePoseRansac: " << e.what() << std::endl;
        }

        return best_pose;
    }

    poselib::CameraPose PoseLibModelEstimator::RefineModel(
        const std::vector<poselib::Point2D> &points1,
        const std::vector<poselib::Point2D> &points2,
        const poselib::Camera &camera1,
        const poselib::Camera &camera2,
        const poselib::CameraPose &initial_pose,
        RefineMethod refine_method)
    {
        poselib::CameraPose refined_pose = initial_pose;

        try
        {
            if (refine_method == RefineMethod::BUNDLE_ADJUST || refine_method == RefineMethod::NONLINEAR)
            {
                // Bundle Adjustment优化
                poselib::BundleOptions bundle_opt;
                bundle_opt.max_iterations = GetOptionAsIndexT("max_iterations", 100);
                bundle_opt.loss_type = (refine_method == RefineMethod::BUNDLE_ADJUST) ? poselib::BundleOptions::LossType::CAUCHY : poselib::BundleOptions::LossType::TRIVIAL;
                bundle_opt.loss_scale = GetOptionAsFloat("loss_scale", 1.0);

                // refine_relpose假设使用归一化坐标，需要转换像素坐标为归一化坐标
                std::vector<poselib::Point2D> norm_points1, norm_points2;
                norm_points1.reserve(points1.size());
                norm_points2.reserve(points2.size());

                for (size_t i = 0; i < points1.size(); ++i)
                {
                    // 将像素坐标转换为归一化坐标
                    poselib::Point2D norm_p1, norm_p2;
                    norm_p1(0) = (points1[i](0) - camera1.params[2]) / camera1.params[0];
                    norm_p1(1) = (points1[i](1) - camera1.params[3]) / camera1.params[1];
                    norm_p2(0) = (points2[i](0) - camera2.params[2]) / camera2.params[0];
                    norm_p2(1) = (points2[i](1) - camera2.params[3]) / camera2.params[1];

                    norm_points1.push_back(norm_p1);
                    norm_points2.push_back(norm_p2);
                }

                // 使用归一化坐标进行Bundle Adjustment
                poselib::refine_relpose(norm_points1, norm_points2, &refined_pose, bundle_opt);
            }
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in RefineModel: " << e.what() << std::endl;
            return initial_pose;
        }

        return refined_pose;
    }

    RelativePose PoseLibModelEstimator::ConvertPoseLibToOpenGV(
        const poselib::CameraPose &pose,
        IndexT view_i,
        IndexT view_j)
    {
        // PoseLib的CameraPose表示从camera1到camera2的变换: P2 = R * P1 + t
        // OpenGV/PoSDK的RelativePose也表示相同的变换，因此直接使用
        Eigen::Matrix3d R = pose.R();
        Eigen::Vector3d t = pose.t.normalized();

        RelativePose relative_pose(
            view_i,
            view_j,
            R.transpose(),
            -R.transpose() * t,
            1.0f); // 默认权重

        return relative_pose;
    }

    bool PoseLibModelEstimator::ConvertToPoseLibPoints(
        const std::shared_ptr<DataSample<IdMatches>> &sample_ptr,
        const std::shared_ptr<FeaturesInfo> &features_ptr,
        const std::shared_ptr<CameraModels> &cameras_ptr,
        const ViewPair &view_pair,
        std::vector<poselib::Point2D> &points1,
        std::vector<poselib::Point2D> &points2,
        poselib::Camera &camera1,
        poselib::Camera &camera2)
    {
        try
        {
            points1.clear();
            points2.clear();
            points1.reserve(sample_ptr->size());
            points2.reserve(sample_ptr->size());

            // 获取真实相机内参
            const CameraModel *cam1 = GetCameraModel(*cameras_ptr, view_pair.first);
            const CameraModel *cam2 = GetCameraModel(*cameras_ptr, view_pair.second);

            if (!cam1 || !cam2)
            {
                PO_LOG_ERR << "Failed to get camera models" << std::endl;
                return false;
            }

            // 设置PoseLib相机参数（使用真实内参）
            camera1.model_id = 0; // PINHOLE
            camera1.params.resize(4);
            camera1.params[0] = cam1->intrinsics.fx;
            camera1.params[1] = cam1->intrinsics.fy;
            camera1.params[2] = cam1->intrinsics.cx;
            camera1.params[3] = cam1->intrinsics.cy;

            camera2.model_id = 0; // PINHOLE
            camera2.params.resize(4);
            camera2.params[0] = cam2->intrinsics.fx;
            camera2.params[1] = cam2->intrinsics.fy;
            camera2.params[2] = cam2->intrinsics.cx;
            camera2.params[3] = cam2->intrinsics.cy;

            // 转换像素坐标（类似OpenCV方式）
            for (const auto &match : *sample_ptr)
            {
                // 获取像素坐标
                const Vector2d &pixel1 = (*features_ptr)[view_pair.first].features[match.i].coord;
                const Vector2d &pixel2 = (*features_ptr)[view_pair.second].features[match.j].coord;

                // 直接使用像素坐标（让PoseLib内部处理归一化）
                poselib::Point2D p1, p2;
                p1 << pixel1.x(), pixel1.y();
                p2 << pixel2.x(), pixel2.y();

                points1.push_back(p1);
                points2.push_back(p2);
            }

            return true;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in ConvertToPoseLibPoints: " << e.what() << std::endl;
            return false;
        }
    }

    bool PoseLibModelEstimator::ConvertToPoseLibBearingVectors(
        const std::shared_ptr<DataSample<IdMatches>> &sample_ptr,
        const std::shared_ptr<FeaturesInfo> &features_ptr,
        const std::shared_ptr<CameraModels> &cameras_ptr,
        const ViewPair &view_pair,
        std::vector<Eigen::Vector3d> &x1,
        std::vector<Eigen::Vector3d> &x2)
    {
        // 复用OpenGV的转换函数，然后转换为PoseLib格式
        opengv::bearingVectors_t bearingVectors1, bearingVectors2;

        if (!Converter::OpenGVConverter::MatchesToBearingVectors(
                *sample_ptr, *features_ptr, *cameras_ptr, view_pair,
                bearingVectors1, bearingVectors2))
        {
            return false;
        }

        // 转换为PoseLib格式
        x1.clear();
        x2.clear();
        x1.reserve(bearingVectors1.size());
        x2.reserve(bearingVectors2.size());

        for (size_t i = 0; i < bearingVectors1.size(); ++i)
        {
            x1.push_back(bearingVectors1[i]);
            x2.push_back(bearingVectors2[i]);
        }

        return true;
    }

    size_t PoseLibModelEstimator::GetMinimumSamplesForAlgorithm(const std::string &algorithm) const
    {
        if (algorithm == "relpose_upright_3pt" || algorithm == "relpose_upright_3pt_ransac" ||
            algorithm == "relpose_upright_planar_3pt")
        {
            return 3;
        }
        else if (algorithm == "relpose_5pt" || algorithm == "relpose_5pt_ransac")
        {
            return 5;
        }
        else if (algorithm == "relpose_7pt" || algorithm == "relpose_7pt_ransac")
        {
            return 7;
        }
        else if (algorithm == "relpose_8pt" || algorithm == "relpose_8pt_ransac")
        {
            return 8;
        }
        else
        {
            // 默认返回5（五点法）
            return 5;
        }
    }

    poselib::RansacOptions PoseLibModelEstimator::CreateRansacOptions() const
    {
        poselib::RansacOptions ransac_opt;
        ransac_opt.max_iterations = GetOptionAsIndexT("ransac_max_iterations", 1000);
        ransac_opt.max_epipolar_error = GetOptionAsFloat("ransac_threshold", 1e-4);
        ransac_opt.progressive_sampling = GetOptionAsBool("progressive_sampling", true);

        return ransac_opt;
    }

    bool PoseLibModelEstimator::IsPoseValid(const poselib::CameraPose &pose) const
    {
        // 检查四元数的有效性
        if (pose.q.norm() < 1e-8)
        {
            return false;
        }

        // 检查平移向量的有效性
        if (pose.t.norm() < 1e-8)
        {
            return false;
        }

        return true;
    }

} // namespace PluginMethods

REGISTRATION_PLUGIN(PluginMethods::PoseLibModelEstimator, "poselib_model_estimator")