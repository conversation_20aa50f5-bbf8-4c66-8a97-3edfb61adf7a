{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 5, "string": "3.31.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-562dadb96430c01d7cf0.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-c95af3a9f8f7ee270ed0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2358b2d22faeaa0d6c2b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-e2dd857c650f64001879.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-c95af3a9f8f7ee270ed0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-562dadb96430c01d7cf0.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-e2dd857c650f64001879.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2358b2d22faeaa0d6c2b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}